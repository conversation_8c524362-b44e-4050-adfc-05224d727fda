import { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

// Mock kullanıcı verileri - Email adresleri küçük harfle saklanır
const mockUsers = {
  '<EMAIL>': {
    username: '<EMAIL>',
    password: 'Batu123*',
    name: 'Admin User',
    email: '<EMAIL>',
    hasInvestment: false,
    investments: []
  },
  '<EMAIL>': {
    username: '<EMAIL>',
    password: 'Atakan*07',
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    hasInvestment: true,
    investments: [
      {
        id: 1,
        package: 'Esnek Plan', // Esnek paket
        packageType: 'flexible',
        amount: 5300,
        currency: 'USD',
        startDate: '2025-05-17', // 17.05.2025
        expectedReturn: 78, // Esnek APR %78
        currentReturn: 6.5, // 1 ay ge<PERSON>ti (17.05 - 17.07)
        monthlyRate: 6.5, // Esnek aylık %6.5
        duration: 12, // Esnek 12 ay
        status: 'active',
        isLocked: false, // Esnek kilitli değil
        dailyEarnings: 11.8, // Günlük kazanç (344.5 / 30 gün ≈ 11.5)
        totalEarnings: 427.1, // 1 ay + 7 gün kazanç

        // Çekim sistemi
        monthlyPayment: 344.5, // Aylık ödeme (5300 * 6.5% = 344.5)
        accumulatedBalance: 82.6, // Birikmiş bakiye (7 gün × 11.8 = 82.6)
        availableWithdraw: 344.5, // Çekime hazır (temmuz ödemesi)
        totalWithdrawn: 0, // Henüz çekim yapılmamış

        // Ödeme tarihleri
        nextPaymentDate: '2025-08-17', // Bir sonraki ödeme
        lastPaymentDate: '2025-07-17', // Son ödeme tarihi (ilk ödeme)

        // Çekim geçmişi (henüz çekim yapılmamış)
        withdrawHistory: []
      }
    ]
  }
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for saved user session
    const savedUser = localStorage.getItem('novayield_user');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);

        // Migration: Eski Atakan Yılmaz'ı Atakan Karakaya'ya güncelle
        if (userData.name === 'Atakan Yılmaz') {
          userData.name = 'Atakan Karakaya';
          localStorage.setItem('novayield_user', JSON.stringify(userData));
        }

        setUser(userData);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Error parsing saved user data:', error);
        localStorage.removeItem('novayield_user');
      }
    }
    setLoading(false);
  }, []);

  const login = (email, password) => {
    // Email adresini küçük harfe çevir (mail adresleri büyük/küçük harf duyarsız)
    const normalizedEmail = email.toLowerCase().trim();
    const userData = mockUsers[normalizedEmail];

    if (userData && userData.password === password) {
      const userSession = {
        username: userData.username,
        name: userData.name,
        email: userData.email,
        hasInvestment: userData.hasInvestment,
        investments: userData.investments
      };

      setUser(userSession);
      setIsAuthenticated(true);
      localStorage.setItem('novayield_user', JSON.stringify(userSession));
      return { success: true };
    }

    return { success: false, error: 'Invalid credentials' };
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('novayield_user');
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
