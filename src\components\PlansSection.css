.plans-section {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 80px 0;
}

.plans-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 30%, var(--accent-primary)06 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, var(--accent-gold)08 0%, transparent 50%);
  opacity: 0.3;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 1.2rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.plan-card {
  position: relative;
  background: linear-gradient(145deg, var(--bg-secondary), var(--bg-primary));
  border: 1px solid var(--border-color);
  border-radius: 1.2rem;
  padding: 1rem;
  text-align: center;
  transition: all 0.4s ease;
  overflow: hidden;
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  min-height: 420px; /* Daha kompakt yükseklik */
}

/* Dark mode blur effect */
[data-theme="dark"] .plan-card {
  filter: blur(0.8px);
  opacity: 0.6;
}

[data-theme="dark"] .plan-card:hover {
  filter: blur(0px);
  opacity: 1;
}

/* Light mode blur effect */
[data-theme="light"] .plan-card {
  filter: blur(1px);
  opacity: 0.5;
}

[data-theme="light"] .plan-card:hover {
  filter: blur(0px);
  opacity: 1;
}

.plan-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 0%, var(--accent-primary)05 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.plan-card:hover::before {
  opacity: 1;
}

.plan-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.plan-card.highlight {
  border: 2px solid var(--accent-primary);
  background: linear-gradient(145deg, var(--bg-secondary), var(--accent-primary)08);
  /* Remove permanent transform and shadow - only apply on hover */
}

/* Removed special filter/opacity rules for highlight cards - let them use normal theme effects */

.plan-card.highlight::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 2rem 2rem 0 0;
}

.plan-card.highlight:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.popular-badge {
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gradient-primary);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 0 0 1rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  box-shadow: var(--shadow-md);
  z-index: 3;
  animation: pulse 2s ease-in-out infinite;
}

.plan-header {
  margin-bottom: 1.5rem;
}

.plan-icon {
  width: 2.5rem;
  height: 2.5rem;
  color: var(--accent-primary);
  margin-bottom: 0.8rem;
}

.plan-icon-svg {
  width: 3rem;
  height: 3rem;
  margin-bottom: 0.8rem;
}

.plan-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  color: var(--text-primary);
}

.plan-pricing {
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: var(--bg-tertiary);
  border-radius: 0.8rem;
  border: 1px solid var(--border-color);
}

.pricing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.pricing-item:last-child {
  margin-bottom: 0;
}

.pricing-item.main {
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
}

.pricing-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.pricing-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.pricing-item.main .pricing-value {
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.plan-usdt-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.feature-usdt-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
  margin-left: 4px;
  vertical-align: middle;
}

.plan-description {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.5;
  font-size: 0.85rem;
}

.plan-features {
  list-style: none;
  margin-bottom: 1.2rem;
  text-align: left;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.4rem 0.8rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  margin-bottom: 0;
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.plan-features .feature-item .feature-icon {
  width: 0.8rem;
  height: 0.8rem;
  color: var(--accent-orange) !important;
  flex-shrink: 0;
}

.plan-cta {
  width: 100%;
  margin-top: auto;
}

/* VIP Card Styles */
.vip-card {
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.1) 0%,
    rgba(255, 165, 0, 0.05) 50%,
    rgba(255, 140, 0, 0.1) 100%);
  border: 2px solid #FFD700;
  box-shadow:
    0 0 20px rgba(255, 215, 0, 0.3),
    0 8px 32px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.vip-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(255, 215, 0, 0.1) 50%,
    transparent 70%);
  animation: vipShimmer 3s ease-in-out infinite;
  pointer-events: none;
}

@keyframes vipShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.vip-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 0 30px rgba(255, 215, 0, 0.5),
    0 12px 40px rgba(0, 0, 0, 0.4);
}

.vip-badge {
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #000;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  animation: vipBadgePulse 2s ease-in-out infinite;
}

@keyframes vipBadgePulse {
  0%, 100% {
    transform: translateX(-50%) scale(1);
  }
  50% {
    transform: translateX(-50%) scale(1.05);
  }
}

.vip-crown {
  filter: drop-shadow(0 0 10px #FFD700);
}

.vip-progress {
  margin: 0.8rem 0;
  padding: 0.8rem;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.vip-progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
}

.vip-progress-label .remaining {
  color: #FFD700;
  font-weight: 600;
}

.vip-progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.vip-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FFD700, #FFA500);
  border-radius: 4px;
  transition: width 0.3s ease;
  animation: vipProgressGlow 2s ease-in-out infinite alternate;
}

@keyframes vipProgressGlow {
  0% {
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
  }
  100% {
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
  }
}

.btn-vip {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: #000;
  border: none;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
  transition: all 0.3s ease;
}

.btn-vip:hover:not(:disabled) {
  background: linear-gradient(135deg, #FFA500, #FF8C00);
  box-shadow: 0 6px 20px rgba(255, 165, 0, 0.6);
  transform: translateY(-2px);
}

.btn-vip:disabled {
  background: #666;
  color: #999;
  cursor: not-allowed;
  box-shadow: none;
}

.plans-note {
  text-align: center;
  margin-top: 2rem;
}

.plans-note p {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .plans-section {
    min-height: 100vh;
    padding: 60px 0 40px 0;
    padding-top: 90px; /* Header + boşluk */
  }

  .plans-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
    max-width: 100%;
  }

  /* Mobile'da blur efektini kaldır - her zaman net görünür */
  [data-theme="dark"] .plan-card,
  [data-theme="light"] .plan-card {
    filter: none !important;
    opacity: 1 !important;
  }

  .plan-card {
    padding: 1rem;
    min-height: 360px; /* Daha kompakt mobile */
  }

  .plan-features {
    grid-template-columns: 1fr 1fr; /* Mobile'da da 2'li grid */
    gap: 0.3rem;
  }

  .plan-card.highlight {
    transform: none;
  }
  
  .plan-card.highlight:hover {
    transform: translateY(-4px);
  }
  
  .plan-icon {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .plan-title {
    font-size: 1rem;
  }

  .plan-monthly,
  .plan-yearly {
    font-size: 0.8rem;
  }

  .plan-description {
    font-size: 0.8rem;
    line-height: 1.4;
  }

  .feature-item {
    font-size: 0.75rem;
    padding: 0.25rem 0;
  }

  .plan-cta {
    padding: 1rem 1.25rem;
    font-size: 0.9rem;
    min-height: 52px;
    border-radius: 0.875rem;
    font-weight: 600;
    margin-top: 1rem;
  }

  .pricing-value {
    font-size: 1rem;
  }

  .pricing-item.main .pricing-value {
    font-size: 1.125rem;
  }
}

@media (max-width: 480px) {
  .plans-section {
    padding: 50px 0 30px 0;
    padding-top: 90px;
  }

  .plans-grid {
    grid-template-columns: 1fr;
    gap: 0.8rem;
  }

  .plan-card {
    padding: 0.9rem;
    margin: 0;
    min-height: 320px;
  }

  .plan-features {
    grid-template-columns: 1fr 1fr; /* 480px'de de 2'li grid kalsın */
    gap: 0.3rem;
  }

  .feature-item {
    font-size: 0.7rem;
    padding: 0.2rem 0;
  }

  .plan-pricing {
    padding: 0.875rem;
  }

  .popular-badge {
    padding: 0.3rem 0.8rem;
    font-size: 0.7rem;
  }

  .plan-icon {
    width: 1.875rem;
    height: 1.875rem;
  }

  .plan-title {
    font-size: 0.95rem;
  }

  .plan-monthly,
  .plan-yearly {
    font-size: 0.75rem;
  }

  .plan-description {
    font-size: 0.75rem;
  }

  .plan-cta {
    padding: 1.125rem 1.5rem;
    font-size: 1rem;
    min-height: 56px;
    border-radius: 1rem;
    font-weight: 700;
    margin-top: 1.25rem;
    width: 100%;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .pricing-value {
    font-size: 0.9rem;
  }

  .pricing-item.main .pricing-value {
    font-size: 1rem;
  }
}
