.apr-calculator-section {
  padding: 60px 0;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.calculator-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.calculator-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, var(--accent-primary)15 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, var(--accent-cyan)10 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, var(--accent-pink)12 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, var(--accent-gold)08 0%, transparent 50%),
    linear-gradient(45deg, transparent 30%, var(--accent-primary)02 50%, transparent 70%);
  opacity: 0.15;
  animation: float 6s ease-in-out infinite;
}

.calculator-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
}

/* Float animation for calculator background */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

.calculator-header {
  text-align: center;
  margin-bottom: 40px;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.calculator-title-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 16px;
}

.calculator-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: 15px;
  flex-shrink: 0;
}

.calculator-icon .icon {
  width: 30px;
  height: 30px;
  color: var(--bg-primary);
}

.calculator-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  flex: 1;
  text-align: left;
}

.calculator-subtitle {
  font-size: 1.2rem;
  color: var(--text-muted);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.calculator-content {
  display: grid;
  grid-template-columns: 380px 1fr;
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.calculator-inputs {
  display: flex;
  flex-direction: column;
  gap: 32px;
  position: relative;
  overflow: hidden;
}

.calculator-inputs-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  opacity: 0.6;
}

.input-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

.input-text {
  color: var(--text-secondary);
  font-size: 1.1rem;
  white-space: nowrap;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-label {
  font-size: 1.1rem;
  font-weight: 600;
  color: #fff;
}

.amount-input {
  position: relative;
  display: flex;
  align-items: center;
  min-width: 200px;
}

.amount-field {
  width: 100%;
  padding: 12px 120px 12px 16px;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.amount-field:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

/* Hide number input spinner arrows */
.amount-field::-webkit-outer-spin-button,
.amount-field::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
.amount-field[type=number] {
  -moz-appearance: textfield;
}

.currency-dropdown {
  position: absolute;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
}

.currency-icon {
  color: var(--accent-green);
  font-size: 0.8rem;
}

.currency-icon-img {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.currency-label {
  color: #4cb5ab !important;
  font-weight: 600;
  font-size: 0.9rem;
}

.earnings-usdt-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
  vertical-align: middle;
  margin-left: 8px;
}

.plan-selector {
  display: flex;
  gap: 8px;
}

.plan-btn {
  padding: 8px 16px;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-muted);
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.plan-btn:hover {
  border-color: var(--accent-primary);
  color: var(--text-primary);
}

.plan-btn.active {
  background: var(--gradient-primary);
  border-color: transparent;
  color: var(--bg-primary);
}

.vip-plan-btn {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1));
  border-color: #FFD700;
  color: #FFD700;
  position: relative;
  overflow: hidden;
}

.vip-plan-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
  transition: left 0.5s ease;
}

.vip-plan-btn:hover::before {
  left: 100%;
}

.vip-plan-btn:hover {
  border-color: #FFA500;
  color: #FFA500;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.vip-plan-btn.active {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-color: transparent;
  color: #000;
  font-weight: 700;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.products-section {
  margin-top: 32px;
  position: relative;
  overflow: hidden;
}

.products-section-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  opacity: 0.4;
}

.products-section h3 {
  color: var(--text-primary);
  font-size: 1.2rem;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.product-card {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  box-shadow: var(--shadow-md);
  position: relative;
  z-index: 1;
}

[data-theme="dark"] .product-card {
  background: linear-gradient(135deg, #1f1f1f 0%, #141414 100%);
  border: 1px solid #404040;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4),
              inset 0 1px 0 rgba(255, 255, 255, 0.03);
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-name {
  color: var(--text-primary);
  font-weight: 600;
}

.product-badge {
  background: var(--accent-green);
  color: var(--bg-primary);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  opacity: 0.9;
}

.product-rate {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-green);
}

.product-breakdown {
  color: var(--text-muted);
  font-size: 0.9rem;
  cursor: pointer;
}

.subscribe-btn {
  background: var(--gradient-primary);
  border: none;
  border-radius: 8px;
  color: var(--bg-primary);
  padding: 12px 24px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
  min-height: 44px;
}

.subscribe-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.calculator-results {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 32px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
}

[data-theme="dark"] .calculator-results {
  background: linear-gradient(145deg, #1f1f1f 0%, #141414 100%);
  border: 1px solid #333333;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5),
              inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.current-plan-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.plan-name {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.plan-rate {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--accent-green);
}

/* Removed earnings-display styles - content now directly in calculator-results */





.earnings-chart {
  width: 100%;
  height: 600px;
  position: relative;
  background: transparent;
  padding: 0;
  margin: 0;
}

.chart-svg {
  width: 100%;
  height: 100%;
}

.earnings-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #b0b0b0;
}

.earnings-features .feature-icon {
  color: #00ff9d;
  font-weight: bold;
}



.calculator-disclaimer {
  text-align: center;
  margin-top: 40px;
  position: relative;
  z-index: 1;
}

.calculator-disclaimer p {
  color: var(--text-muted);
  font-size: 0.9rem;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .apr-calculator-section {
    min-height: 100vh;
    padding: 40px 0;
    padding-top: 90px; /* Header + boşluk */
  }

  .calculator-header {
    margin-bottom: 30px;
  }

  .calculator-title-row {
    flex-direction: column;
    gap: 15px;
  }

  .calculator-title {
    font-size: 1.8rem;
    text-align: center;
  }

  .calculator-icon {
    width: 50px;
    height: 50px;
  }

  .calculator-icon .icon {
    width: 25px;
    height: 25px;
  }

  .calculator-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .calculator-subtitle {
    font-size: 1rem;
  }

  .input-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 24px;
  }

  .amount-input {
    min-width: 100%;
  }

  .plan-selector {
    width: 100%;
  }

  .plan-btn {
    flex: 1;
    padding: 6px 12px;
    font-size: 0.85rem;
  }

  .calculator-results {
    padding: 24px;
  }

  .earnings-chart {
    height: 400px;
  }

  .subscribe-btn {
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
    min-height: 48px;
    border-radius: 0.75rem;
    width: 100%;
    max-width: 200px;
    margin: 0.5rem auto;
  }
}

@media (max-width: 480px) {
  .subscribe-btn {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    min-height: 52px;
    border-radius: 1rem;
    width: 100%;
    max-width: 240px;
    margin: 0.75rem auto;
    font-weight: 700;
  }
}
