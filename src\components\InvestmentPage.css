/* Investment Page Overlay - Modern Design */
.investment-page-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(102, 126, 234, 0.2) 50%,
    rgba(118, 75, 162, 0.3) 100%
  );
  backdrop-filter: blur(15px) saturate(1.8);
  -webkit-backdrop-filter: blur(15px) saturate(1.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  overflow: hidden;
  animation: overlayFadeIn 0.5s ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(15px) saturate(1.8);
    -webkit-backdrop-filter: blur(15px) saturate(1.8);
  }
}

/* Main Container - Ultra Modern Glass Effect */
.investment-page {
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
  backdrop-filter: blur(30px) saturate(1.8) brightness(1.1);
  -webkit-backdrop-filter: blur(30px) saturate(1.8) brightness(1.1);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 2rem;
  width: 90vw;
  max-width: 1200px;
  height: 85vh;
  max-height: 750px;
  overflow: hidden;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset,
    0 2px 4px rgba(255, 255, 255, 0.15) inset;
  animation: slideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Glass reflection effect */
.investment-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.02) 50%,
    transparent 100%
  );
  border-radius: 2rem 2rem 0 0;
  pointer-events: none;
  z-index: 1;
}

/* Dark Mode Ultra Glass Effect */
[data-theme="dark"] .investment-page {
  background:
    linear-gradient(135deg,
      rgba(15, 23, 42, 0.4) 0%,
      rgba(30, 41, 59, 0.2) 100%
    );
  backdrop-filter: blur(35px) saturate(2) brightness(1.2);
  -webkit-backdrop-filter: blur(35px) saturate(2) brightness(1.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.03) inset,
    0 2px 4px rgba(255, 255, 255, 0.08) inset;
}

[data-theme="dark"] .investment-page::before {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.01) 50%,
    transparent 100%
  );
}

/* Light Mode Ultra Glass Effect */
[data-theme="light"] .investment-page {
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.25) 0%,
      rgba(248, 250, 252, 0.15) 100%
    );
  backdrop-filter: blur(28px) saturate(1.6) brightness(1.05);
  -webkit-backdrop-filter: blur(28px) saturate(1.6) brightness(1.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset,
    0 2px 4px rgba(255, 255, 255, 0.2) inset;
}

[data-theme="light"] .investment-page::before {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.03) 50%,
    transparent 100%
  );
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9) rotateX(10deg);
    filter: blur(10px);
  }
  50% {
    opacity: 0.8;
    transform: translateY(10px) scale(0.98) rotateX(2deg);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
    filter: blur(0px);
  }
}

/* Modern bounce effect for mobile */
@keyframes mobileSlideUp {
  from {
    opacity: 0;
    transform: translateY(100%) scale(0.95);
  }
  60% {
    opacity: 1;
    transform: translateY(-10px) scale(1.02);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Header - Ultra Glass Effect */
.investment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 3rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border-radius: 2rem 2rem 0 0;
  position: relative;
  z-index: 2;
}

/* Dark Mode Header */
[data-theme="dark"] .investment-header {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* Light Mode Header */
[data-theme="light"] .investment-header {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-btn {
  width: 48px;
  height: 48px;
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.15s ease;
  color: var(--text-secondary);
}

.back-btn:hover {
  background: var(--accent-primary);
  color: white;
  transform: translateX(-2px);
  transition: all 0.15s ease;
}

.back-btn:hover svg {
  color: white;
  transition: color 0.15s ease;
}

.investment-header h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
}

.investment-header svg {
  color: var(--accent-primary);
}

/* Step Indicator */
.step-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.step {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  border: 2px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-muted);
}

.step.active {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: white;
}

.step-line {
  width: 60px;
  height: 2px;
  background: var(--border-color);
  transition: all 0.3s ease;
}

.step-line.active {
  background: var(--accent-primary);
}

/* Content */
.investment-content {
  display: flex;
  gap: 0.1rem;
  flex: 1;
  min-height: 0;
  max-height: 100%;
  overflow: hidden;
}

/* Packages Section */
.packages-section {
  flex: 1;
  padding: 2rem;
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
}

/* Modern Scrollbar Styles */
.packages-section::-webkit-scrollbar,
.calculation-section::-webkit-scrollbar,
.summary-section::-webkit-scrollbar,
.payment-section::-webkit-scrollbar {
  width: 6px;
}

.packages-section::-webkit-scrollbar-track,
.calculation-section::-webkit-scrollbar-track,
.summary-section::-webkit-scrollbar-track,
.payment-section::-webkit-scrollbar-track {
  background: transparent;
}

.packages-section::-webkit-scrollbar-thumb,
.calculation-section::-webkit-scrollbar-thumb,
.summary-section::-webkit-scrollbar-thumb,
.payment-section::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.packages-section::-webkit-scrollbar-thumb:hover,
.calculation-section::-webkit-scrollbar-thumb:hover,
.summary-section::-webkit-scrollbar-thumb:hover,
.payment-section::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

/* Firefox scrollbar */
.packages-section,
.calculation-section,
.summary-section,
.payment-section {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.packages-section h2 {
  margin: 0 0 2rem 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

.packages-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Package Cards - Header ile Aynı Glass Effect */
.package-card {
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1.5rem;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Package Cards - Header ile Aynı */
[data-theme="dark"] .package-card {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Package Cards - Header ile Aynı */
[data-theme="light"] .package-card {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.package-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--border-color);
  transition: all 0.3s ease;
}

.package-card.blue::before {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.package-card.purple::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.package-card.gold::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.package-card:hover {
  transform: translateY(-4px);
  border-color: rgba(var(--accent-primary-rgb), 0.4);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset,
    0 0 20px rgba(var(--accent-primary-rgb), 0.1);
}

[data-theme="dark"] .package-card:hover {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset,
    0 0 20px rgba(var(--accent-primary-rgb), 0.15);
}

.package-card.selected {
  border-color: rgba(var(--accent-primary-rgb), 0.6);
  background: rgba(var(--accent-primary-rgb), 0.08);
  transform: translateY(-4px);
  box-shadow:
    0 8px 32px rgba(var(--accent-primary-rgb), 0.2),
    0 0 0 1px rgba(var(--accent-primary-rgb), 0.2) inset,
    0 0 30px rgba(var(--accent-primary-rgb), 0.15);
}

[data-theme="dark"] .package-card.selected {
  background: rgba(var(--accent-primary-rgb), 0.12);
  box-shadow:
    0 8px 32px rgba(var(--accent-primary-rgb), 0.3),
    0 0 0 1px rgba(var(--accent-primary-rgb), 0.3) inset,
    0 0 30px rgba(var(--accent-primary-rgb), 0.2);
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.package-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.package-apr {
  background: var(--gradient-primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-weight: 600;
  font-size: 0.875rem;
}

.package-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.info-item svg {
  color: var(--accent-primary);
}

.package-features {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.package-features li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.package-features svg {
  color: var(--accent-green);
  flex-shrink: 0;
}

/* Calculation Section - Header ile Aynı Glass Effect */
.calculation-section {
  width: 380px;
  flex-shrink: 0;
  padding: 1.5rem;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1.5rem;
  margin: 1rem;
  overflow-y: auto;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Calculation Section */
[data-theme="dark"] .calculation-section {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Calculation Section */
[data-theme="light"] .calculation-section {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.calculation-section h2 {
  margin: 0 0 1.25rem 0;
  color: var(--text-primary);
  font-size: 1.4rem;
  font-weight: 600;
}

.amount-input-section {
  margin-bottom: 2rem;
}

.amount-input-section label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
}

.amount-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.currency {
  position: absolute;
  left: 0.875rem;
  color: var(--text-muted);
  font-weight: 600;
  font-size: 0.9rem;
  z-index: 1;
}

.amount-input {
  width: 100%;
  padding: 0.875rem 0.875rem 0.875rem 2.5rem;
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Amount Input */
[data-theme="dark"] .amount-input {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Amount Input */
[data-theme="light"] .amount-input {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.amount-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.currency-label {
  position: absolute;
  right: 1rem;
  color: var(--text-muted);
  font-weight: 600;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid #ef4444;
  border-radius: 0.75rem;
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.75rem;
}

/* Returns Preview */
.returns-preview {
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1.5rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Returns Preview */
[data-theme="dark"] .returns-preview {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Returns Preview */
[data-theme="light"] .returns-preview {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.returns-preview h3 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.returns-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.return-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 0.75rem;
  font-size: 0.85rem;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Return Item */
[data-theme="dark"] .return-item {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Return Item */
[data-theme="light"] .return-item {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.return-item.highlight {
  background: var(--gradient-primary) !important;
  border-color: var(--accent-primary);
  color: white !important;
}

.return-item.highlight .label,
.return-item.highlight .value {
  color: white !important;
}

.return-item .label {
  color: #8B7FB8;
}

.return-item .value {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1rem;
}

.return-item.highlight .value {
  color: var(--accent-primary);
  font-size: 1.1rem;
}

/* Continue Button */
.continue-btn {
  width: 100%;
  padding: 0.875rem;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.continue-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(99, 102, 241, 0.4);
}

.continue-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* No Selection State */
.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  color: var(--text-muted);
}

.no-selection svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-selection h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-secondary);
}

.no-selection p {
  margin: 0;
  font-size: 0.9rem;
}

/* Summary Section (Step 2) */
.summary-section {
  flex: 1;
  padding: 2rem;
  border-right: 1px solid var(--border-color);
  overflow: hidden;
}

.summary-section h2 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

.summary-card {
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1.5rem;
  padding: 1.25rem;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Summary Card */
[data-theme="dark"] .summary-card {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Summary Card */
[data-theme="light"] .summary-card {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  gap: 1rem;
}

.summary-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  flex: 1;
  line-height: 1.3;
}

.apr-badge {
  background: var(--gradient-primary);
  color: white;
  padding: 0.4rem 0.875rem;
  border-radius: 1.5rem;
  font-weight: 600;
  font-size: 0.8rem;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: fit-content;
  height: fit-content;
  flex-shrink: 0;
}

.summary-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 0.75rem;
  margin-bottom: 0;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Detail Row */
[data-theme="dark"] .detail-row {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Detail Row */
[data-theme="light"] .detail-row {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.detail-row:not(:last-child) {
  border-bottom: none; /* Artık gerek yok çünkü her satır ayrı kart */
}

.detail-row.highlight {
  background: rgba(99, 102, 241, 0.1);
  padding: 1rem;
  border-radius: 0.75rem;
  border: 1px solid var(--accent-primary);
  font-weight: 600;
  color: var(--accent-primary);
}



/* Summary and Network Layout */
.summary-and-network {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.summary-card {
  flex: 1;
  min-width: 0;
}

/* Network Selection Right (İşlem özeti yanında) */
.network-selection-right {
  width: 280px;
  flex-shrink: 0;
}

.network-selection-right h3 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.networks-grid-right {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Kompakt Düzenle Butonu */
.edit-btn-compact {
  width: 100%;
  padding: 0.75rem;
  margin-top: 1rem;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Edit Button */
[data-theme="dark"] .edit-btn-compact {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Edit Button */
[data-theme="light"] .edit-btn-compact {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.edit-btn-compact:hover {
  border-color: var(--accent-primary);
  color: var(--accent-primary);
  transform: translateY(-1px);
}

.network-card-right {
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Network Card Right */
[data-theme="dark"] .network-card-right {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Network Card Right */
[data-theme="light"] .network-card-right {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.network-card-right:hover {
  border-color: var(--accent-primary);
}

.network-card-right.selected {
  border-color: #8B7FB8;
  background: linear-gradient(135deg, #8B7FB8 0%, #6366F1 100%);
  color: white;
  box-shadow:
    0 8px 32px rgba(139, 127, 184, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

.network-card-right.selected .network-name-right,
.network-card-right.selected .network-fee-right {
  color: white;
}

.network-header-right {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.75rem;
}

.network-name-right {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
  flex: 1;
}

.network-fee-right {
  font-size: 0.8rem;
  color: var(--text-muted);
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Network Fee Right */
[data-theme="dark"] .network-fee-right {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Network Fee Right */
[data-theme="light"] .network-fee-right {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

/* Payment Section (Step 2) - Sadece ödeme detayları */
.payment-section {
  width: 500px;
  flex-shrink: 0;
  padding: 1.5rem;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Payment Section */
[data-theme="dark"] .payment-section {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Payment Section */
[data-theme="light"] .payment-section {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.payment-section h2 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

/* No Network Selected State */
.no-network-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  color: var(--text-muted);
  flex: 1;
}

.no-network-selected svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-network-selected h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-secondary);
}

.no-network-selected p {
  margin: 0;
  font-size: 0.9rem;
}

.network-selection h3 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.networks-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.network-card {
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Network Card */
[data-theme="dark"] .network-card {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Network Card */
[data-theme="light"] .network-card {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.network-card:hover {
  border-color: var(--accent-primary);
  transform: translateY(-2px);
}

.network-card.selected {
  border-color: #8B7FB8;
  background: linear-gradient(135deg, #8B7FB8 0%, #6366F1 100%);
  color: white;
  box-shadow:
    0 8px 32px rgba(139, 127, 184, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

.network-card.selected .network-name,
.network-card.selected .network-fee {
  color: white;
}

.network-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.network-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

.network-details p {
  margin: 0 0 0.5rem 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.network-info {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: var(--text-muted);
}

/* Payment Details */
.payment-details {
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Payment Details */
[data-theme="dark"] .payment-details {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Payment Details */
[data-theme="light"] .payment-details {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.payment-details h3 {
  margin: 0 0 1.5rem 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.wallet-section {
  margin-bottom: 1rem;
}

.wallet-section label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.wallet-input {
  display: flex;
  gap: 0.75rem;
}

.wallet-address {
  flex: 1;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Wallet Address */
[data-theme="dark"] .wallet-address {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Wallet Address */
[data-theme="light"] .wallet-address {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.copy-btn {
  padding: 1rem 1.5rem;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Copy Button */
[data-theme="dark"] .copy-btn {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Copy Button */
[data-theme="light"] .copy-btn {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.copy-btn:hover {
  border-color: var(--accent-primary);
  color: var(--accent-primary);
  transform: translateY(-1px);
}

.copy-btn.copied {
  background: var(--accent-green);
  border-color: var(--accent-green);
  color: white;
}

.qr-section {
  margin-bottom: 1rem;
}

.qr-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem 1rem;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1.5rem;
  text-align: center;
  color: var(--text-muted);
  margin: 0.5rem 0;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode QR Placeholder */
[data-theme="dark"] .qr-placeholder {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode QR Placeholder */
[data-theme="light"] .qr-placeholder {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.qr-placeholder p {
  margin: 0.5rem 0 0.25rem 0;
  font-weight: 600;
}

.qr-placeholder span {
  font-size: 0.8rem;
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

/* Dark Mode Payment Info */
[data-theme="dark"] .payment-info {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset;
}

/* Light Mode Payment Info */
[data-theme="light"] .payment-info {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.15) inset;
}

.payment-info .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.payment-info .info-item span:first-child {
  color: var(--text-secondary);
}

.payment-info .info-item span:last-child {
  color: var(--text-primary);
  font-weight: 600;
}

.confirm-payment-btn {
  width: 100%;
  padding: 1rem;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.confirm-payment-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(99, 102, 241, 0.4);
}

/* Responsive Design */
@media (max-width: 1400px) {
  .investment-page {
    width: 90vw;
  }

  .calculation-section {
    width: 400px;
  }

  .payment-section {
    width: 450px;
  }
}

@media (max-width: 1200px) {
  .investment-content {
    flex-direction: column;
  }

  .packages-section,
  .calculation-section,
  .summary-section,
  .payment-section {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .calculation-section {
    margin: 0;
    border-radius: 0;
  }

  .payment-section {
    background:
      linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.03) 100%
      );
    backdrop-filter: blur(20px) saturate(1.5);
    -webkit-backdrop-filter: blur(20px) saturate(1.5);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.05),
      0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }

  /* Dark Mode Payment Section - Responsive */
  [data-theme="dark"] .payment-section {
    background:
      linear-gradient(135deg,
        rgba(30, 41, 59, 0.3) 0%,
        rgba(15, 23, 42, 0.2) 100%
      );
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.05) inset;
  }

  /* Light Mode Payment Section - Responsive */
  [data-theme="light"] .payment-section {
    background:
      linear-gradient(135deg,
        rgba(248, 250, 252, 0.4) 0%,
        rgba(255, 255, 255, 0.2) 100%
      );
    border: 1px solid rgba(0, 0, 0, 0.03);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.03),
      0 0 0 1px rgba(255, 255, 255, 0.15) inset;
  }
}

/* Mobile Popup Design - Modern & Clean */
@media (max-width: 768px) {
  .investment-page-overlay {
    padding: 1rem;
    align-items: center;
    justify-content: center;
    overflow: auto;
  }

  .investment-page {
    width: 95vw;
    max-width: 420px;
    height: auto;
    max-height: 85vh;
    border-radius: 1.5rem;
    margin: 0;
    background:
      linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.9) 100%
      );
    backdrop-filter: blur(20px) saturate(1.5);
    -webkit-backdrop-filter: blur(20px) saturate(1.5);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    animation: mobileSlideUp 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    overflow: hidden;
  }

  /* Mobilde 2. aşama için özel düzenleme */
  .investment-content {
    flex-direction: column;
    overflow-y: auto;
    max-height: calc(85vh - 120px);
  }

  /* Mobilde summary ve payment section'ları */
  .summary-section,
  .payment-section {
    flex: none;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem;
    overflow-y: visible;
  }

  .payment-section {
    border-bottom: none;
    padding-bottom: 2rem;
  }

  /* Mobilde summary-and-network dikey düzen */
  .summary-and-network {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  /* Ağ seçimi mobilde summary-card altında */
  .network-selection-right {
    order: 2;
    width: 100%;
    flex-shrink: 1;
  }

  /* Mobilde network kartları tam genişlik */
  .networks-grid-right {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .network-card-right {
    width: 100%;
    padding: 1rem;
  }

  /* QR kod alanına scroll */
  .qr-section {
    margin-top: 1rem;
    scroll-margin-top: 2rem;
  }

  .investment-header {
    background: transparent;
    backdrop-filter: none;
    padding: 1rem 1.25rem 0.75rem;
    border-radius: 1.5rem 1.5rem 0 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    position: relative;
    top: 0;
    z-index: 100;
  }

  /* Mobile Close Button */
  .close-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 101;
  }

  .close-btn:hover {
    background: rgba(0, 0, 0, 0.2);
    transform: scale(1.1);
  }

  .close-btn svg {
    width: 16px;
    height: 16px;
    color: #666;
  }

  .investment-header h1 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #667eea;
  }

  .step-indicator {
    gap: 0.5rem;
  }

  .step {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
  }

  .step.active {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    transform: scale(1.05);
  }

  .step-line {
    width: 25px;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 1px;
  }

  .step-line.active {
    background: linear-gradient(90deg, #4CAF50, #45a049);
  }

  .investment-content {
    padding: 0;
    gap: 0;
    background: transparent;
    overflow-y: auto;
    max-height: calc(85vh - 80px);
    display: flex;
    flex-direction: column;
  }

  /* Mobile Step 2 Layout - Alt Alta Düzenleme */
  .investment-content.step-2 {
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  .investment-content.step-2 .summary-section,
  .investment-content.step-2 .payment-section {
    width: 100%;
    flex: none;
    border-right: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  }

  .investment-content.step-2 .payment-section {
    border-bottom: none;
  }

  /* Mobile Package Selection - Compact */
  .packages-section {
    background: transparent;
    padding: 1rem 1.25rem;
    margin: 0;
    border-radius: 0;
    max-height: none;
    overflow: visible;
  }

  .packages-section h2 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 1rem;
    text-align: center;
  }

  .packages-grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .package-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    border: 1.5px solid rgba(102, 126, 234, 0.15);
    border-radius: 1rem;
    padding: 1.25rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 80px;
    cursor: pointer;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .package-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
  }

  .package-card * {
    position: relative;
    z-index: 1;
  }

  .package-header h3 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.25rem;
  }

  .package-card .apr {
    font-size: 1.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0.25rem 0;
  }

  .package-card .duration {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
  }

  .package-card .min-amount {
    font-size: 0.8rem;
    color: #888;
    margin-top: 0.25rem;
  }

  /* Mobile Calculation Section - Compact */
  .calculation-section {
    background: transparent;
    padding: 1rem 1.25rem;
    margin: 0;
    border-radius: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    display: block !important;
    width: 100% !important;
    flex-shrink: 0;
  }

  .calculation-section h2 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 1rem;
    text-align: center;
  }

  .amount-input-section {
    margin-bottom: 1.25rem;
  }

  .amount-input-section label {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    display: block;
  }

  .amount-input-wrapper {
    position: relative;
    margin-bottom: 0.75rem;
  }

  .amount-input {
    width: 100%;
    padding: 0.875rem 0.875rem 0.875rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border: 1.5px solid rgba(102, 126, 234, 0.2);
    border-radius: 0.75rem;
    background: rgba(248, 249, 250, 0.8);
    transition: all 0.3s ease;
    box-sizing: border-box;
  }

  .amount-input:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    outline: none;
  }

  .currency {
    position: absolute;
    left: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1rem;
    font-weight: 600;
    color: #667eea;
  }

  .currency-label {
    position: absolute;
    right: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.8rem;
    font-weight: 500;
    color: #888;
  }

  .calculation-results {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1.25rem;
  }

  .result-item {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    padding: 0.875rem;
    border-radius: 0.75rem;
    border: 1px solid rgba(102, 126, 234, 0.1);
    text-align: center;
  }

  .result-item .label {
    font-size: 0.75rem;
    color: #666;
    font-weight: 500;
    margin-bottom: 0.25rem;
    display: block;
  }

  .result-item .value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #667eea;
  }

  .result-item.highlight {
    grid-column: 1 / -1;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
  }

  .result-item.highlight .label {
    color: rgba(255, 255, 255, 0.9);
  }

  .result-item.highlight .value {
    color: white;
    font-size: 1.2rem;
  }

  .continue-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  }

  .continue-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }

  .continue-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
  }

  /* Mobile No Selection State */
  .no-selection {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 3rem 1.5rem;
    color: #666;
  }

  .no-selection svg {
    color: #667eea;
    margin-bottom: 1rem;
  }

  .no-selection h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
  }

  .no-selection p {
    font-size: 1rem;
    color: #666;
    line-height: 1.5;
  }

  /* Enhanced Mobile Interactions */
  .package-card:active {
    transform: translateY(-2px) scale(0.98);
    transition: transform 0.1s ease;
  }

  .network-card:active,
  .network-card-right:active {
    transform: translateY(-2px) scale(0.98);
    transition: transform 0.1s ease;
  }

  .continue-btn:active {
    transform: translateY(0) scale(0.98);
    transition: transform 0.1s ease;
  }

  .confirm-payment-btn:active {
    transform: translateY(0) scale(0.98);
    transition: transform 0.1s ease;
  }

  /* Smooth section transitions */
  .packages-section,
  .calculation-section,
  .summary-section,
  .payment-section {
    transition: all 0.3s ease;
  }

  /* Loading states */
  .continue-btn:disabled {
    position: relative;
    overflow: hidden;
  }

  .continue-btn:disabled::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  /* Mobile Summary Section - Kompakt Paket Özeti */
  .summary-section {
    background: transparent;
    padding: 1rem 1.25rem 0.75rem;
    margin: 0;
    border-radius: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    order: 1; /* İlk sırada */
  }

  .summary-section h2 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.75rem;
    text-align: center;
  }

  .summary-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #e0e0e0;
    border-radius: 1.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .summary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .summary-header h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #667eea;
  }

  .apr-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.9rem;
    font-weight: 600;
    align-self: flex-start;
  }

  .summary-details {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e0e0e0;
  }

  .summary-item:last-child {
    border-bottom: none;
    font-weight: 700;
    font-size: 1.1rem;
  }

  .summary-item .label {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
  }

  .summary-item .value {
    font-size: 1rem;
    font-weight: 600;
    color: #667eea;
  }

  .edit-btn-compact {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .edit-btn-compact:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }

  .payment-section {
    background: transparent;
    padding: 1rem 1.25rem;
    margin: 0;
    border-radius: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    order: 2; /* İkinci sırada - Ağ Seçimi */
  }

  .payment-section h2 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.75rem;
    text-align: center;
  }

  .networks-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .network-card,
  .network-card-right {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    border: 1.5px solid rgba(102, 126, 234, 0.15);
    border-radius: 1rem;
    padding: 1.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 70px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .network-card.selected,
  .network-card-right.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
  }

  .network-card *,
  .network-card-right * {
    position: relative;
    z-index: 1;
  }

  .network-card h4,
  .network-card-right h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.25rem;
  }

  .network-card .fee,
  .network-card-right .fee {
    font-size: 0.8rem;
    color: #666;
    font-weight: 500;
  }

  /* Mobile Payment Details - QR/Wallet Bölümü (En Alta) */
  .payment-details {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    border: 1px solid rgba(102, 126, 234, 0.1);
    border-radius: 1rem;
    padding: 1.25rem;
    margin: 1rem 0;
    order: 3; /* Üçüncü sırada - QR/Wallet */
  }

  .payment-details h3 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.75rem;
    text-align: center;
  }

  .wallet-input {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .wallet-address {
    background: rgba(248, 249, 250, 0.8);
    border: 1.5px solid rgba(102, 126, 234, 0.2);
    border-radius: 0.75rem;
    padding: 0.875rem;
    font-size: 0.85rem;
    font-family: 'Courier New', monospace;
    word-break: break-all;
    color: #333;
    line-height: 1.4;
  }

  .copy-btn {
    background: transparent;
    color: #667eea; /* Default hover rengi */
    border: 1.5px solid rgba(102, 126, 234, 0.3);
    padding: 0.75rem;
    border-radius: 0.75rem;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;
    min-width: 80px;
  }

  .copy-btn:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  }

  .copy-btn.copied {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border-color: #4CAF50;
  }

  .qr-section {
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .qr-code {
    width: 150px;
    height: 150px;
    border: 2px solid #e0e0e0;
    border-radius: 1rem;
    margin: 0 auto;
    background: white;
    padding: 0.5rem;
  }

  .confirm-payment-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3);
  }

  .confirm-payment-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
  }

  /* Error message styling */
  .error-message {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 1rem;
    border-radius: 1rem;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
  }
}

/* Extra Small Devices (Phones, 480px and down) */
@media (max-width: 480px) {
  .investment-page-overlay {
    padding: 0.5rem;
  }

  .investment-page {
    border-radius: 1.5rem; /* Oval kenarlıkları koru */
    width: 98vw;
    max-width: 400px;
  }

  .investment-header {
    padding: 0.75rem;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .header-left {
    width: 100%;
    justify-content: space-between;
  }

  .investment-header h1 {
    font-size: 1.1rem;
  }

  .step-indicator {
    order: 2;
    align-self: center;
  }

  .step {
    width: 24px;
    height: 24px;
    font-size: 0.7rem;
  }

  .step-line {
    width: 20px;
  }

  .packages-section,
  .calculation-section,
  .summary-section,
  .payment-section {
    padding: 0.75rem;
  }

  .package-card {
    padding: 1rem;
  }

  .package-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .package-apr {
    align-self: flex-start;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  .amount-input {
    padding: 0.75rem 0.75rem 0.75rem 2.25rem;
    font-size: 0.9rem;
  }

  .currency {
    left: 0.75rem;
    font-size: 0.8rem;
  }

  .returns-preview {
    padding: 1rem;
  }

  .return-item {
    padding: 0.625rem;
    font-size: 0.8rem;
  }

  .continue-btn {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .summary-header h3 {
    font-size: 1.1rem;
  }

  .detail-row {
    padding: 0.75rem;
    font-size: 0.9rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    border: 1px solid rgba(102, 126, 234, 0.1);
    border-radius: 0.75rem;
    margin-bottom: 0.5rem;
  }

  .detail-row .label {
    font-weight: 600;
    color: #333;
  }

  .detail-row .value {
    font-weight: 700;
    color: #667eea;
  }

  .detail-row.highlight {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
  }

  .detail-row.highlight .label,
  .detail-row.highlight .value {
    color: white;
  }

  /* Mobile Summary Container */
  .investment-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  /* Mobile Edit Button */
  .edit-btn-compact {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border: 1px solid rgba(102, 126, 234, 0.2);
    color: #667eea;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    align-self: flex-start;
  }

  .edit-btn-compact:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
    transform: translateY(-1px);
  }

  .network-card,
  .network-card-right {
    padding: 0.875rem;
  }

  .payment-details {
    padding: 1rem;
  }

  .wallet-address {
    padding: 0.75rem;
    font-size: 0.8rem;
  }

  .copy-btn {
    padding: 0.625rem;
    font-size: 0.8rem;
  }

  .qr-placeholder {
    padding: 1rem;
  }

  .confirm-payment-btn {
    padding: 0.875rem;
    font-size: 0.9rem;
  }
}

/* Touch-friendly improvements for mobile */
@media (max-width: 768px) {
  .package-card,
  .network-card,
  .network-card-right {
    min-height: 44px; /* iOS recommended touch target */
    touch-action: manipulation;
  }

  .back-btn,
  .copy-btn,
  .continue-btn,
  .confirm-payment-btn,
  .edit-btn-compact {
    min-height: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Prevent zoom on input focus */
  .amount-input,
  .wallet-address {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better scrolling on mobile */
  .packages-section,
  .calculation-section,
  .summary-section,
  .payment-section {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Reduce animations on mobile for better performance */
  .package-card,
  .network-card,
  .network-card-right {
    transition: transform 0.2s ease, border-color 0.2s ease;
  }

  .package-card:hover,
  .network-card:hover,
  .network-card-right:hover {
    transform: none; /* Remove hover transforms on mobile */
  }

  /* Active states for better touch feedback */
  .package-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .continue-btn:active,
  .confirm-payment-btn:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

/* Landscape orientation for mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .investment-page {
    height: 100vh;
    max-height: none;
  }

  .investment-content {
    overflow-y: auto;
  }

  .packages-section,
  .summary-section {
    max-height: calc(100vh - 120px);
    overflow-y: auto;
  }

  .investment-header {
    padding: 0.75rem 1rem;
  }
}

/* Very small screens (320px and below) */
@media (max-width: 320px) {
  .investment-header h1 {
    font-size: 1rem;
  }

  .step {
    width: 20px;
    height: 20px;
    font-size: 0.65rem;
  }

  .step-line {
    width: 15px;
  }

  .package-card,
  .summary-card,
  .payment-details {
    padding: 0.75rem;
  }

  .amount-input {
    padding: 0.625rem 0.625rem 0.625rem 2rem;
  }

  .currency {
    left: 0.625rem;
  }
}
