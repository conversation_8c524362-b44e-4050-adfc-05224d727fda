.footer {
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: 4rem 0 2rem;
  margin-top: 4rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-brand {
  max-width: 300px;
}

.footer-logo {
  font-size: 1.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.footer-tagline {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.social-link:hover {
  background-color: var(--accent-primary);
  color: white;
  transform: translateY(-2px);
  border-color: var(--accent-primary);
}

.footer-links h4,
.footer-contact h4,
.footer-newsletter h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.footer-links ul {
  list-style: none;
}

.footer-links li {
  margin-bottom: 0.75rem;
}

.footer-link {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: var(--accent-primary);
}

.contact-info p {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: 0.75rem;
}

.contact-info svg {
  color: var(--accent-primary);
}

.footer-newsletter p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.newsletter-form {
  display: flex;
  gap: 0.5rem;
}

.newsletter-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.newsletter-input:focus {
  outline: none;
  border-color: var(--accent-primary);
}

.newsletter-btn {
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.newsletter-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.footer-bottom {
  margin-top: 3rem;
}

.footer-divider {
  height: 1px;
  background-color: var(--border-color);
  margin-bottom: 2rem;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin: 0;
}

.footer-badges {
  display: flex;
  gap: 1rem;
}

.badge {
  padding: 0.4rem 0.8rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* SSL Badge - Yeşil */
.badge:nth-child(1) {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

/* USDT Badge - Altın */
.badge:nth-child(2) {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

/* 7/24 Destek Badge - Mavi */
.badge:nth-child(3) {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

/* Hover efektleri */
.badge:hover {
  transform: translateY(-1px);
  transition: all 0.3s ease;
}

.badge:nth-child(1):hover {
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.badge:nth-child(2):hover {
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.badge:nth-child(3):hover {
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
  
  .footer-brand {
    max-width: none;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 3rem 0 1.5rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .footer-brand {
    max-width: none;
  }
  
  .social-links {
    justify-content: center;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .footer-badges {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 2rem 0 1rem;
  }
  
  .newsletter-form {
    flex-direction: column;
  }
  
  .newsletter-btn {
    padding: 0.875rem 1rem;
  }
  
  .footer-badges {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .social-links {
    gap: 0.75rem;
  }
  
  .social-link {
    width: 2.25rem;
    height: 2.25rem;
  }
}
