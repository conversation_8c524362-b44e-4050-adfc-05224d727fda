/* Settings Overlay - Yatırım modal tarzında */
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(102, 126, 234, 0.2) 50%,
    rgba(118, 75, 162, 0.3) 100%
  );
  backdrop-filter: blur(15px) saturate(1.8);
  -webkit-backdrop-filter: blur(15px) saturate(1.8);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow: hidden;
  animation: overlayFadeIn 0.5s ease-out;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(15px) saturate(1.8);
    -webkit-backdrop-filter: blur(15px) saturate(1.8);
  }
}

/* Settings Container - Yatırım modal tarzında cam tasarım */
.settings-container {
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
  backdrop-filter: blur(30px) saturate(1.8) brightness(1.1);
  -webkit-backdrop-filter: blur(30px) saturate(1.8) brightness(1.1);
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 2rem;
  width: 90vw;
  max-width: 1200px;
  height: 85vh;
  max-height: 750px;
  overflow: hidden;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset,
    0 2px 4px rgba(255, 255, 255, 0.15) inset;
  animation: slideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  position: relative;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dark Mode Ultra Glass Effect */
[data-theme="dark"] .settings-container {
  background:
    linear-gradient(135deg,
      rgba(15, 23, 42, 0.4) 0%,
      rgba(30, 41, 59, 0.2) 100%
    );
  backdrop-filter: blur(35px) saturate(2) brightness(1.2);
  -webkit-backdrop-filter: blur(35px) saturate(2) brightness(1.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.03) inset,
    0 2px 4px rgba(255, 255, 255, 0.08) inset;
}

/* Light Mode Ultra Glass Effect */
[data-theme="light"] .settings-container {
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.25) 0%,
      rgba(248, 250, 252, 0.15) 100%
    );
  backdrop-filter: blur(28px) saturate(1.6) brightness(1.05);
  -webkit-backdrop-filter: blur(28px) saturate(1.6) brightness(1.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset,
    0 2px 4px rgba(255, 255, 255, 0.2) inset;
}

/* Settings Header - Yatırım modal tarzında */
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 3rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border-radius: 2rem 2rem 0 0;
  position: relative;
  z-index: 2;
}

/* Dark Mode Header */
[data-theme="dark"] .settings-header {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* Light Mode Header */
[data-theme="light"] .settings-header {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.settings-header h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.settings-back-btn,
.settings-close-btn {
  width: 44px;
  height: 44px;
  border: 2px solid var(--border-color);
  background: var(--bg-tertiary);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

.settings-back-btn:hover,
.settings-close-btn:hover {
  border-color: var(--accent-primary);
  background: var(--accent-primary);
  color: white;
  transform: translateY(-1px);
}

/* Settings Content */
.settings-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Settings Sidebar - Yatırım modal tarzında */
.settings-sidebar {
  width: 320px;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.03) 100%
    );
  backdrop-filter: blur(20px) saturate(1.5);
  -webkit-backdrop-filter: blur(20px) saturate(1.5);
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  padding: 2rem 0 1.5rem 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* Dark Mode Sidebar */
[data-theme="dark"] .settings-sidebar {
  background:
    linear-gradient(135deg,
      rgba(30, 41, 59, 0.3) 0%,
      rgba(15, 23, 42, 0.2) 100%
    );
  border-right: 1px solid rgba(255, 255, 255, 0.05);
}

/* Light Mode Sidebar */
[data-theme="light"] .settings-sidebar {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 100%
    );
  border-right: 1px solid rgba(0, 0, 0, 0.03);
}

.settings-tab {
  width: auto;
  max-width: 90%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.8rem 1rem;
  border: none;
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 0.95rem;
  font-weight: 500;
  border-left: 4px solid transparent;
  backdrop-filter: blur(10px);
  margin: 0.25rem 0.5rem;
  border-radius: 8px;
}

.settings-tab:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  border-left: 4px solid var(--accent-primary);
  transform: translateX(2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.settings-tab.active {
  background: var(--accent-primary);
  color: white;
  border-left: 4px solid var(--accent-primary);
  font-weight: 600;
  box-shadow: 0 0 20px rgba(var(--accent-primary-rgb), 0.3);
  position: relative;
}

[data-theme="dark"] .settings-tab {
  background: rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .settings-tab:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Settings Main - Yatırım modal tarzında */
.settings-main {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background:
    linear-gradient(135deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.02) 100%
    );
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
}

/* Dark Mode Main */
[data-theme="dark"] .settings-main {
  background:
    linear-gradient(135deg,
      rgba(15, 23, 42, 0.2) 0%,
      rgba(30, 41, 59, 0.1) 100%
    );
}

/* Light Mode Main */
[data-theme="light"] .settings-main {
  background:
    linear-gradient(135deg,
      rgba(248, 250, 252, 0.2) 0%,
      rgba(255, 255, 255, 0.1) 100%
    );
}

/* Settings Section */
.settings-section {
  max-width: 600px;
  margin: 0 auto;
}

.settings-section h2 {
  margin: 0 0 2rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Settings Field */
.settings-field {
  margin-bottom: 2rem;
}

.settings-field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.settings-field small {
  display: block;
  margin-top: 0.5rem;
  color: var(--text-muted);
  font-size: 0.8rem;
}

/* Settings Input Group - Yatırım modal tarzında */
.settings-input-group {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.settings-input-group:focus-within {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(var(--accent-primary-rgb), 0.1);
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .settings-input-group {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .settings-input-group:focus-within {
  background: rgba(0, 0, 0, 0.3);
}

.settings-input-group svg {
  margin-left: 1rem;
  color: var(--text-muted);
  flex-shrink: 0;
}

.settings-input {
  flex: 1;
  padding: 1rem 1rem 1rem 0.5rem;
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
}

.settings-input:focus {
  background: rgba(255, 255, 255, 0.05);
}

.settings-input.disabled {
  color: var(--text-muted);
  cursor: not-allowed;
}

.settings-input::placeholder {
  color: var(--text-muted);
}

[data-theme="dark"] .settings-input:focus {
  background: rgba(0, 0, 0, 0.2);
}

/* Action Buttons */
.settings-action-btn,
.settings-toggle-btn {
  width: 44px;
  height: 44px;
  border: none;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: 0.5rem;
  margin-right: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.settings-action-btn:hover,
.settings-toggle-btn:hover {
  background: var(--accent-primary);
  color: white;
}

.settings-action-btn.copied {
  background: #10b981;
  color: white;
}

/* Submit Button */
.settings-submit-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: var(--gradient-primary);
  border: none;
  border-radius: 0.75rem;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.settings-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.settings-submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Message */
.settings-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 0.75rem;
  margin-bottom: 2rem;
  font-weight: 500;
}

.settings-message.success {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.settings-message.error {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.settings-message.info {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Verification Section */
.verification-section {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-top: 2rem;
}

.verification-section h3 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.verification-section p {
  margin: 0 0 1rem 0;
  color: var(--text-secondary);
}

.verification-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: 0.75rem;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1.25rem;
  text-align: center;
  letter-spacing: 0.5rem;
  margin-bottom: 1rem;
}

.verification-btn {
  width: 100%;
  padding: 1rem;
  background: var(--accent-primary);
  border: none;
  border-radius: 0.75rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.verification-btn:hover {
  background: var(--accent-secondary);
  transform: translateY(-1px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .settings-header {
    padding: 1rem;
  }

  .settings-header h1 {
    font-size: 1.5rem;
  }

  .settings-content {
    flex-direction: column;
  }

  .settings-sidebar {
    width: 100%;
    display: flex;
    overflow-x: auto;
    padding: 1rem 0;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .settings-tab {
    flex-shrink: 0;
    min-width: 140px;
    padding: 0.75rem 1rem;
    border-left: none;
    border-bottom: 3px solid transparent;
    text-align: center;
  }

  .settings-tab:hover,
  .settings-tab.active {
    border-left: none;
    border-bottom-color: var(--accent-primary);
  }

  .settings-main {
    padding: 1.5rem 1rem;
  }

  .settings-section {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .settings-header {
    padding: 0.75rem;
  }

  .settings-header h1 {
    font-size: 1.25rem;
  }

  .settings-back-btn,
  .settings-close-btn {
    width: 40px;
    height: 40px;
  }

  .settings-main {
    padding: 1rem;
  }

  .settings-section h2 {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
  }

  .settings-field {
    margin-bottom: 1.5rem;
  }

  .settings-input-group svg {
    margin-left: 0.75rem;
  }

  .settings-input {
    padding: 0.875rem 0.75rem 0.875rem 0.5rem;
    font-size: 0.9rem;
  }

  .settings-action-btn,
  .settings-toggle-btn {
    width: 40px;
    height: 40px;
    margin-right: 0.375rem;
  }

  .verification-input {
    font-size: 1.125rem;
    letter-spacing: 0.25rem;
  }
}

/* Wallet Balance */
.wallet-balance {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 1.5rem;
}

.balance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.balance-item:last-child {
  border-bottom: none;
}

.balance-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.balance-value {
  color: var(--text-primary);
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* Settings Toggle */
.settings-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
}

.toggle-info {
  flex: 1;
}

.toggle-info label {
  margin: 0 0 0.25rem 0;
  display: block;
  font-weight: 600;
  color: var(--text-primary);
}

.toggle-info small {
  margin: 0;
  color: var(--text-muted);
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin: 0;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

/* Theme Options */
.theme-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.theme-option {
  position: relative;
}

.theme-option input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.theme-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.theme-card:hover {
  border-color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.theme-option input:checked + .theme-card {
  border-color: var(--accent-primary);
  background: rgba(var(--accent-primary-rgb), 0.1);
  box-shadow: 0 0 20px rgba(var(--accent-primary-rgb), 0.3);
}

[data-theme="dark"] .theme-card {
  background: rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.05);
}

.theme-preview {
  width: 60px;
  height: 40px;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.theme-header {
  height: 12px;
  width: 100%;
}

.theme-content {
  height: 28px;
  width: 100%;
}

.theme-header.light {
  background: #f8fafc;
}

.theme-content.light {
  background: #ffffff;
}

.theme-header.dark {
  background: #1e293b;
}

.theme-content.dark {
  background: #0f172a;
}

.theme-card span {
  font-weight: 600;
  color: var(--text-primary);
}

/* Settings Select */
.settings-select {
  flex: 1;
  padding: 1rem 1rem 1rem 0.5rem;
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-size: 1rem;
  outline: none;
  cursor: pointer;
}

.settings-select option {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* Language Options */
.language-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.language-option:hover {
  border-color: var(--accent-primary);
  transform: translateX(4px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.language-option.active {
  border-color: var(--accent-primary);
  background: rgba(var(--accent-primary-rgb), 0.1);
  box-shadow: 0 0 15px rgba(var(--accent-primary-rgb), 0.2);
}

[data-theme="dark"] .language-option {
  background: rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.05);
}

.language-flag {
  width: 24px;
  height: 18px;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.language-name {
  flex: 1;
  font-weight: 500;
  color: var(--text-primary);
}

.language-check {
  color: var(--accent-primary);
}

/* Wallet Networks */
.wallet-networks {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.wallet-network {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.wallet-network:hover {
  border-color: var(--accent-primary);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .wallet-network {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.network-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.network-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
}

.network-type {
  background: var(--accent-primary);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}
