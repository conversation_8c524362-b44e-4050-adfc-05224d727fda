/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  transition: all 0.3s ease;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  gap: 2rem;
}



.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 800;
}

/* Navigation */
.navigation {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.desktop-nav {
  display: flex;
}

.mobile-navigation {
  display: none;
  flex-direction: column;
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: 1rem 0;
  gap: 0.5rem;
  animation: slideDown 0.3s ease-out;
  backdrop-filter: blur(10px);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  display: block;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: var(--accent-primary);
  background: var(--accent-primary)10;
}

.mobile-menu-toggle {
  display: none;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mobile-menu-toggle:hover {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  min-height: 44px; /* Language selector ile aynı yükseklik */
}

.nav-link:hover,
.nav-link.active {
  color: var(--accent-primary);
  background-color: var(--bg-secondary);
  transform: translateY(-1px);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 80%;
}

.nav-link.active {
  font-weight: 600;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Desktop ve Mobile visibility */
@media (min-width: 769px) {
  .mobile-language {
    display: none;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .desktop-nav {
    display: none;
  }

  .mobile-language {
    display: flex;
  }
}

.language-selector {
  position: relative;
  display: flex;
  align-items: center;
}

.language-dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem; /* Nav-link ile aynı padding */
  border: 2px solid var(--border-color);
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 4rem;
  min-height: 44px; /* Nav-link ile aynı yükseklik */
}

.language-dropdown-trigger:hover {
  border-color: var(--accent-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.language-dropdown-trigger .flag-image {
  width: 1.5rem;
  height: 1.125rem;
  object-fit: cover;
  border-radius: 0.25rem;
}

.dropdown-arrow {
  color: var(--text-secondary);
  transition: transform 0.3s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.language-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  background: var(--bg-secondary);
  border: 2px solid var(--border-color);
  border-radius: 0.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 10rem;
  overflow: hidden;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.language-option:hover {
  background: var(--bg-tertiary);
}

.language-option.active {
  background: var(--accent-primary);
  color: white;
}

/* Dark mode için dil seçici kontrast iyileştirmesi */
[data-theme="dark"] .language-dropdown {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-xl);
}

[data-theme="dark"] .language-option {
  color: var(--text-primary);
}

[data-theme="dark"] .language-option:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

[data-theme="dark"] .language-option.active {
  background: var(--accent-primary);
  color: white;
}

[data-theme="dark"] .language-name {
  color: var(--text-primary);
  font-weight: 500;
}

.language-option .flag-image {
  width: 1.5rem;
  height: 1.125rem;
  object-fit: cover;
  border-radius: 0.25rem;
}

.language-name {
  font-size: 0.875rem;
  font-weight: 500;
}

.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-toggle:hover {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

/* Main content spacing for fixed header */
main {
  margin-top: 80px;
}

/* Floating Menu System */
.floating-menu-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

/* Main Support Button */
.floating-support-btn {
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Light mode - mavi/beyaz/gri */
[data-theme="light"] .floating-support-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

[data-theme="light"] .floating-support-btn:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), 0 0 20px rgba(59, 130, 246, 0.4);
}

[data-theme="light"] .floating-support-btn.active {
  background: linear-gradient(135deg, #1e40af, #1e3a8a);
  transform: rotate(45deg);
}

/* Dark mode - turuncu */
[data-theme="dark"] .floating-support-btn {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
}

[data-theme="dark"] .floating-support-btn:hover {
  background: linear-gradient(135deg, #ea580c, #dc2626);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), 0 0 20px rgba(249, 115, 22, 0.4);
}

[data-theme="dark"] .floating-support-btn.active {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: rotate(45deg);
}

.floating-support-btn:active {
  transform: translateY(0) scale(0.95);
}

/* Floating Menu Items */
.floating-menu-items {
  position: absolute;
  bottom: 0;
  right: 0;
}

.floating-menu-item {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeInUp 0.3s ease;
  position: absolute;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Floating Menu Items - Saat Dizilimi (Ana butondan 80px uzaklık) */

/* Telegram Button - Saat 10 yönü */
.telegram-btn {
  bottom: 69px; /* 80 * sin(60°) ≈ 69px */
  right: 40px;  /* 80 * cos(60°) = 40px */
}

[data-theme="light"] .telegram-btn {
  background: linear-gradient(135deg, #0088cc, #006699);
  color: white;
}

[data-theme="light"] .telegram-btn:hover {
  background: linear-gradient(135deg, #006699, #004466);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(0, 136, 204, 0.4);
}

[data-theme="dark"] .telegram-btn {
  background: linear-gradient(135deg, #0088cc, #006699);
  color: white;
}

[data-theme="dark"] .telegram-btn:hover {
  background: linear-gradient(135deg, #006699, #004466);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(0, 136, 204, 0.4);
}

/* WhatsApp Button - Saat 12 yönü */
.whatsapp-btn {
  bottom: 80px; /* Tam üst */
  right: 5px;   /* Ortalanmış */
}

[data-theme="light"] .whatsapp-btn {
  background: linear-gradient(135deg, #25d366, #128c7e);
  color: white;
}

[data-theme="light"] .whatsapp-btn:hover {
  background: linear-gradient(135deg, #128c7e, #075e54);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(37, 211, 102, 0.4);
}

[data-theme="dark"] .whatsapp-btn {
  background: linear-gradient(135deg, #25d366, #128c7e);
  color: white;
}

[data-theme="dark"] .whatsapp-btn:hover {
  background: linear-gradient(135deg, #128c7e, #075e54);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(37, 211, 102, 0.4);
}

/* Contact Button - Saat 2 yönü */
.contact-btn {
  bottom: 69px; /* 80 * sin(60°) ≈ 69px */
  right: -30px; /* 80 * cos(60°) = 40px (negatif çünkü sağa doğru) */
}

[data-theme="light"] .contact-btn {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
}

[data-theme="light"] .contact-btn:hover {
  background: linear-gradient(135deg, #4f46e5, #4338ca);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(99, 102, 241, 0.4);
}

[data-theme="dark"] .contact-btn {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

[data-theme="dark"] .contact-btn:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(245, 158, 11, 0.4);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .desktop-nav {
    gap: 1.5rem;
  }

  .nav-link {
    font-size: 0.9rem;
    padding: 0.375rem 0.75rem;
    min-height: 40px; /* Tablet için daha küçük */
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0.75rem 0;
    gap: 1rem;
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    width: 100%;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    justify-self: start;
    grid-column: 1;
  }

  /* Desktop navigation gizle */
  .desktop-nav {
    display: none;
  }

  /* Mobile menu toggle sadece header-controls içinde göster */
  .mobile-menu-toggle {
    display: flex;
  }

  /* Mobile navigation göster */
  .mobile-navigation {
    display: flex;
  }

  .logo h2 {
    font-size: 1.25rem;
  }

  /* Language selector ortada */
  .mobile-language {
    justify-self: center;
    display: flex;
    align-items: center;
    grid-column: 2;
  }

  .header-controls {
    gap: 0.75rem;
    display: flex;
    align-items: center;
    justify-self: end;
    grid-column: 3;
  }

  /* Desktop language selector'ı gizle */
  .header-controls .language-selector {
    display: none;
  }

  .language-dropdown-trigger {
    padding: 0.5rem;
    min-width: 3.5rem;
    min-height: 44px;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .language-dropdown {
    min-width: 8rem;
    right: 0;
  }

  .language-name {
    font-size: 0.85rem;
    font-weight: 500;
  }

  .theme-toggle {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mobile-menu-toggle {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  main {
    margin-top: 70px;
  }

  .floating-menu-container {
    bottom: 1.5rem;
    right: 1.5rem;
  }

  .floating-support-btn {
    width: 52px;
    height: 52px;
  }

  .floating-menu-item {
    width: 44px;
    height: 44px;
  }

  /* Telegram - Saat 10 yönü (70px uzaklık) */
  .telegram-btn {
    bottom: 61px; /* 70 * sin(60°) ≈ 61px */
    right: 35px;  /* 70 * cos(60°) = 35px */
  }

  /* WhatsApp - Saat 12 yönü */
  .whatsapp-btn {
    bottom: 70px; /* Tam üst */
    right: 2px;   /* Ortalanmış */
  }

  /* Contact - Saat 2 yönü */
  .contact-btn {
    bottom: 61px; /* 70 * sin(60°) ≈ 61px */
    right: -27px; /* 70 * cos(60°) = 35px (negatif) */
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0.5rem 0;
  }

  .header-left {
    gap: 0.5rem;
  }

  .logo h2 {
    font-size: 1.125rem;
  }

  .header-controls {
    gap: 0.5rem;
  }

  /* Language selector küçük ekranlarda daha kompakt */
  .mobile-language {
    justify-self: center;
    display: flex;
  }

  .language-dropdown-trigger {
    padding: 0.25rem;
    min-width: 2.75rem;
  }

  .language-dropdown {
    min-width: 7rem;
    right: 0;
  }

  .language-name {
    font-size: 0.8rem;
  }

  .theme-toggle {
    width: 2rem;
    height: 2rem;
  }

  .mobile-menu-toggle {
    width: 2rem;
    height: 2rem;
  }

  .mobile-nav-link {
    font-size: 0.9rem;
    padding: 0.625rem 1rem;
  }

  main {
    margin-top: 65px;
  }

  .floating-menu-container {
    bottom: 1rem;
    right: 1rem;
  }

  .floating-support-btn {
    width: 48px;
    height: 48px;
  }

  .floating-menu-item {
    width: 40px;
    height: 40px;
  }

  /* Telegram - Saat 10 yönü (60px uzaklık) */
  .telegram-btn {
    bottom: 52px; /* 60 * sin(60°) ≈ 52px */
    right: 30px;  /* 60 * cos(60°) = 30px */
  }

  /* WhatsApp - Saat 12 yönü */
  .whatsapp-btn {
    bottom: 60px; /* Tam üst */
    right: 4px;   /* Ortalanmış */
  }

  /* Contact - Saat 2 yönü */
  .contact-btn {
    bottom: 52px; /* 60 * sin(60°) ≈ 52px */
    right: -22px; /* 60 * cos(60°) = 30px (negatif) */
  }
}

/* Loading Screen */
.loading-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
