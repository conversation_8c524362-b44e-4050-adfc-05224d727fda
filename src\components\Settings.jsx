import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../context/AuthContext';
import {
  User,
  Mail,
  Lock,
  Wallet,
  Shield,
  Bell,
  Palette,
  Globe,
  Eye,
  EyeOff,
  Save,
  X,
  Check,
  AlertCircle,
  Copy,
  ArrowLeft
} from 'lucide-react';
import './Settings.css';

const Settings = ({ onClose }) => {
  const { t, i18n } = useTranslation();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [showPassword, setShowPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const [verificationCode, setVerificationCode] = useState('');
  const [showVerification, setShowVerification] = useState(false);
  const [copiedWallet, setCopiedWallet] = useState(false);

  // Form states
  const [formData, setFormData] = useState({
    email: user?.email || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Tema ve dil ayarları
  const [currentTheme, setCurrentTheme] = useState(
    document.documentElement.getAttribute('data-theme') || 'light'
  );
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);
  const [forceUpdate, setForceUpdate] = useState(0);

  // Dil seçenekleri
  const languages = [
    { code: 'tr', name: 'Türkçe', flag: '/flags/turkey.png' },
    { code: 'en', name: 'English', flag: '/flags/usa.png' },
    { code: 'ru', name: 'Русский', flag: '/flags/russia.png' }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleEmailChange = async () => {
    setIsLoading(true);
    try {
      // Email değiştirme API çağrısı
      setMessage({ type: 'success', text: 'Email başarıyla güncellendi!' });
    } catch (error) {
      setMessage({ type: 'error', text: 'Email güncellenirken hata oluştu!' });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordChange = async () => {
    if (formData.newPassword !== formData.confirmPassword) {
      setMessage({ type: 'error', text: 'Yeni şifreler eşleşmiyor!' });
      return;
    }

    setIsLoading(true);
    try {
      // Doğrulama kodu gönder
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      setVerificationCode(code);
      setShowVerification(true);
      setMessage({ type: 'info', text: `Doğrulama kodu email adresinize gönderildi: ${code}` });
    } catch (error) {
      setMessage({ type: 'error', text: 'Doğrulama kodu gönderilirken hata oluştu!' });
    } finally {
      setIsLoading(false);
    }
  };

  const copyWalletAddress = () => {
    const walletAddress = "**********************************"; // Örnek Bitcoin adresi
    navigator.clipboard.writeText(walletAddress);
    setCopiedWallet(true);
    setTimeout(() => setCopiedWallet(false), 2000);
  };

  // Tema değiştirme
  const handleThemeChange = (theme) => {
    setCurrentTheme(theme);
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);

    // Dashboard'taki tema state'ini de güncelle
    window.dispatchEvent(new CustomEvent('themeChange', { detail: theme }));
  };

  // Dil değişikliğini dinle
  useEffect(() => {
    const handleLanguageChanged = () => {
      setCurrentLanguage(i18n.language);
      setForceUpdate(prev => prev + 1); // Force re-render
    };

    i18n.on('languageChanged', handleLanguageChanged);
    return () => {
      i18n.off('languageChanged', handleLanguageChanged);
    };
  }, []);

  // Dil değiştirme
  const handleLanguageChange = async (languageCode) => {
    setCurrentLanguage(languageCode);
    await i18n.changeLanguage(languageCode);
    localStorage.setItem('language', languageCode);
    setForceUpdate(prev => prev + 1); // Force re-render
  };

  // Tabs array'ini useMemo ile optimize edelim
  const tabs = useMemo(() => [
    { id: 'profile', label: t('settings.profile'), icon: User },
    { id: 'security', label: t('settings.security'), icon: Shield },
    { id: 'wallet', label: t('settings.wallet'), icon: Wallet },
    { id: 'notifications', label: t('settings.notifications'), icon: Bell },
    { id: 'appearance', label: t('settings.appearance'), icon: Palette }
  ], [t, forceUpdate]);

  return (
    <div className="settings-overlay">


      <div className="settings-container">
        <div className="settings-header">
          <button className="settings-back-btn" onClick={onClose}>
            <ArrowLeft size={20} />
          </button>
          <h1>{t('settings.title')}</h1>
          <button className="settings-close-btn" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="settings-content">
          {/* Sidebar */}
          <div className="settings-sidebar">
            {tabs.map(tab => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  className={`settings-tab ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  <IconComponent size={20} />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>

          {/* Main Content */}
          <div className="settings-main">
            {message.text && (
              <div className={`settings-message ${message.type}`}>
                <AlertCircle size={16} />
                <span>{message.text}</span>
              </div>
            )}

            {/* Profile Tab */}
            {activeTab === 'profile' && (
              <div className="settings-section">
                <h2>{t('settings.profile')}</h2>

                <div className="settings-field">
                  <label>{t('settings.profile.name')}</label>
                  <div className="settings-input-group">
                    <User size={20} />
                    <input
                      type="text"
                      value={user?.name || 'Atakan Karakaya'}
                      disabled
                      className="settings-input disabled"
                    />
                  </div>
                  <small>{t('settings.profile.name_note')}</small>
                </div>

                <div className="settings-field">
                  <label>{t('settings.profile.email')}</label>
                  <div className="settings-input-group">
                    <Mail size={20} />
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="settings-input"
                      placeholder={t('settings.profile.email')}
                    />
                    <button
                      className="settings-action-btn"
                      onClick={handleEmailChange}
                      disabled={isLoading}
                    >
                      <Save size={16} />
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Security Tab */}
            {activeTab === 'security' && (
              <div className="settings-section">
                <h2>{t('settings.security')}</h2>

                <div className="settings-field">
                  <label>{t('settings.security.current_password')}</label>
                  <div className="settings-input-group">
                    <Lock size={20} />
                    <input
                      type={showPassword ? 'text' : 'password'}
                      name="currentPassword"
                      value={formData.currentPassword}
                      onChange={handleInputChange}
                      className="settings-input"
                      placeholder={t('settings.security.current_password')}
                    />
                    <button
                      className="settings-toggle-btn"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                </div>

                <div className="settings-field">
                  <label>{t('settings.security.new_password')}</label>
                  <div className="settings-input-group">
                    <Lock size={20} />
                    <input
                      type={showNewPassword ? 'text' : 'password'}
                      name="newPassword"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                      className="settings-input"
                      placeholder={t('settings.security.new_password')}
                    />
                    <button
                      className="settings-toggle-btn"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    >
                      {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                </div>

                <div className="settings-field">
                  <label>{t('settings.security.confirm_password')}</label>
                  <div className="settings-input-group">
                    <Lock size={20} />
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      className="settings-input"
                      placeholder={t('settings.security.confirm_password')}
                    />
                    <button
                      className="settings-toggle-btn"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                </div>

                <button
                  className="settings-submit-btn"
                  onClick={handlePasswordChange}
                  disabled={isLoading || !formData.currentPassword || !formData.newPassword}
                >
                  {t('settings.security.update_password')}
                </button>

                {showVerification && (
                  <div className="verification-section">
                    <h3>{t('settings.security.verification_title')}</h3>
                    <p>{t('settings.security.verification_desc')}</p>
                    <input
                      type="text"
                      maxLength="6"
                      className="verification-input"
                      placeholder="000000"
                    />
                    <button className="verification-btn">{t('settings.security.verify')}</button>
                  </div>
                )}
              </div>
            )}

            {/* Wallet Tab */}
            {activeTab === 'wallet' && (
              <div className="settings-section">
                <h2>{t('settings.wallet')}</h2>

                <div className="settings-field">
                  <label>{t('settings.wallet.address')}</label>
                  <div className="wallet-networks">
                    <div className="wallet-network">
                      <div className="network-header">
                        <span className="network-name">Bitcoin (BTC)</span>
                        <span className="network-type">BTC Network</span>
                      </div>
                      <div className="settings-input-group">
                        <Wallet size={20} />
                        <input
                          type="text"
                          value="**********************************"
                          readOnly
                          disabled
                          className="settings-input disabled"
                        />
                        <button
                          className={`settings-action-btn ${copiedWallet ? 'copied' : ''}`}
                          onClick={copyWalletAddress}
                        >
                          {copiedWallet ? <Check size={16} /> : <Copy size={16} />}
                        </button>
                      </div>
                    </div>

                    <div className="wallet-network">
                      <div className="network-header">
                        <span className="network-name">Ethereum (ETH)</span>
                        <span className="network-type">ERC-20</span>
                      </div>
                      <div className="settings-input-group">
                        <Wallet size={20} />
                        <input
                          type="text"
                          value="******************************************"
                          readOnly
                          disabled
                          className="settings-input disabled"
                        />
                        <button className="settings-action-btn">
                          <Copy size={16} />
                        </button>
                      </div>
                    </div>

                    <div className="wallet-network">
                      <div className="network-header">
                        <span className="network-name">Tether (USDT)</span>
                        <span className="network-type">TRC-20</span>
                      </div>
                      <div className="settings-input-group">
                        <Wallet size={20} />
                        <input
                          type="text"
                          value="TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE"
                          readOnly
                          disabled
                          className="settings-input disabled"
                        />
                        <button className="settings-action-btn">
                          <Copy size={16} />
                        </button>
                      </div>
                    </div>

                    <div className="wallet-network">
                      <div className="network-header">
                        <span className="network-name">Binance Smart Chain</span>
                        <span className="network-type">BEP-20</span>
                      </div>
                      <div className="settings-input-group">
                        <Wallet size={20} />
                        <input
                          type="text"
                          value="******************************************"
                          readOnly
                          disabled
                          className="settings-input disabled"
                        />
                        <button className="settings-action-btn">
                          <Copy size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                  <small>{t('settings.wallet.address_note')}</small>
                </div>

                <div className="settings-field">
                  <label>{t('settings.wallet.balance')}</label>
                  <div className="wallet-balance">
                    <div className="balance-item">
                      <span className="balance-label">Bitcoin (BTC)</span>
                      <span className="balance-value">0.00000000 BTC</span>
                    </div>
                    <div className="balance-item">
                      <span className="balance-label">Ethereum (ETH)</span>
                      <span className="balance-value">0.00000000 ETH</span>
                    </div>
                    <div className="balance-item">
                      <span className="balance-label">USDT</span>
                      <span className="balance-value">0.00 USDT</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Notifications Tab */}
            {activeTab === 'notifications' && (
              <div className="settings-section">
                <h2>{t('settings.notifications')}</h2>

                <div className="settings-field">
                  <div className="settings-toggle">
                    <div className="toggle-info">
                      <label>{t('settings.notifications.email')}</label>
                      <small>{t('settings.notifications.email_desc')}</small>
                    </div>
                    <label className="toggle-switch">
                      <input type="checkbox" defaultChecked />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>
                </div>

                <div className="settings-field">
                  <div className="settings-toggle">
                    <div className="toggle-info">
                      <label>{t('settings.notifications.sms')}</label>
                      <small>{t('settings.notifications.sms_desc')}</small>
                    </div>
                    <label className="toggle-switch">
                      <input type="checkbox" defaultChecked />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>
                </div>

                <div className="settings-field">
                  <div className="settings-toggle">
                    <div className="toggle-info">
                      <label>{t('settings.notifications.push')}</label>
                      <small>{t('settings.notifications.push_desc')}</small>
                    </div>
                    <label className="toggle-switch">
                      <input type="checkbox" />
                      <span className="toggle-slider"></span>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* Appearance Tab */}
            {activeTab === 'appearance' && (
              <div className="settings-section">
                <h2>{t('settings.appearance')}</h2>

                <div className="settings-field">
                  <label>{t('settings.appearance.theme')}</label>
                  <div className="theme-options">
                    <div className="theme-option">
                      <input
                        type="radio"
                        name="theme"
                        id="light"
                        checked={currentTheme === 'light'}
                        onChange={() => handleThemeChange('light')}
                      />
                      <label htmlFor="light" className="theme-card light">
                        <div className="theme-preview">
                          <div className="theme-header light"></div>
                          <div className="theme-content light"></div>
                        </div>
                        <span>{t('settings.appearance.theme_light')}</span>
                      </label>
                    </div>
                    <div className="theme-option">
                      <input
                        type="radio"
                        name="theme"
                        id="dark"
                        checked={currentTheme === 'dark'}
                        onChange={() => handleThemeChange('dark')}
                      />
                      <label htmlFor="dark" className="theme-card dark">
                        <div className="theme-preview">
                          <div className="theme-header dark"></div>
                          <div className="theme-content dark"></div>
                        </div>
                        <span>{t('settings.appearance.theme_dark')}</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="settings-field">
                  <label>{t('settings.appearance.language')}</label>
                  <div className="language-options">
                    {languages.map((lang) => (
                      <div
                        key={lang.code}
                        className={`language-option ${currentLanguage === lang.code ? 'active' : ''}`}
                        onClick={() => handleLanguageChange(lang.code)}
                      >
                        <img src={lang.flag} alt={lang.name} className="language-flag" />
                        <span className="language-name">{lang.name}</span>
                        {currentLanguage === lang.code && <Check size={16} className="language-check" />}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
