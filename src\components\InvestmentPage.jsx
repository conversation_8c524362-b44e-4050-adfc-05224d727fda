import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  ArrowLeft,
  TrendingUp,
  DollarSign,
  Calendar,
  CheckCircle,
  AlertCircle,
  Copy,
  QrCode,
  Wallet,
  Network,
  CreditCard,
  Edit3
} from 'lucide-react';
import './InvestmentPage.css';

const InvestmentPage = ({ onClose }) => {
  const { t } = useTranslation();
  const [step, setStep] = useState(1); // 1: <PERSON>et Seçimi, 2: <PERSON>de<PERSON>
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [amount, setAmount] = useState('');

  // Arka plan scroll'unu engelle
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
      document.documentElement.style.overflow = 'unset';
    };
  }, []);
  const [selectedNetwork, setSelectedNetwork] = useState(null);
  const [copied, setCopied] = useState(false);

  // Yatırım paketleri
  const packages = [
    {
      id: 1,
      name: t('investment.package.beginner'),
      apr: 78,
      duration: 12,
      minAmount: 1000,
      maxAmount: 49999,
      features: [
        t('investment.features.instant'),
        t('investment.features.monthly'),
        t('investment.features.minimum'),
        t('investment.features.support')
      ],
      color: 'blue'
    },
    {
      id: 2,
      name: t('investment.package.professional'),
      apr: 102,
      duration: 12,
      minAmount: 2500,
      maxAmount: 99999,
      features: [
        t('investment.features.monthly'),
        t('investment.features.premium_support'),
        'Özel yatırım danışmanı',
        'Aylık detaylı raporlar'
      ],
      color: 'purple'
    },
    {
      id: 3,
      name: t('investment.package.premium'),
      apr: 156,
      duration: 12,
      minAmount: 10000,
      maxAmount: 999999,
      features: [
        'En yüksek getiri oranı',
        'Kişisel portföy yöneticisi',
        t('investment.features.premium_support'),
        'Özel etkinlik davetleri'
      ],
      color: 'gold'
    }
  ];

  // Ağ seçenekleri
  const networks = [
    {
      id: 'trc20',
      name: 'TRC20',
      fullName: 'Tron Network',
      symbol: 'USDT',
      address: 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
      fee: '1 USDT',
      time: '1-3 dakika'
    },
    {
      id: 'erc20',
      name: 'ERC20',
      fullName: 'Ethereum Network',
      symbol: 'USDT',
      address: '******************************************',
      fee: '15-30 USDT',
      time: '5-15 dakika'
    },
    {
      id: 'bep20',
      name: 'BEP20',
      fullName: 'Binance Smart Chain',
      symbol: 'USDT',
      address: '******************************************',
      fee: '1-3 USDT',
      time: '1-5 dakika'
    }
  ];

  // Hesaplamalar
  const calculateReturns = () => {
    if (!selectedPackage || !amount) return null;
    
    const investmentAmount = parseFloat(amount);
    const monthlyReturn = (investmentAmount * selectedPackage.apr / 100) / 12;
    const totalReturn = monthlyReturn * selectedPackage.duration;
    const finalAmount = investmentAmount + totalReturn;
    
    return {
      monthlyReturn,
      totalReturn,
      finalAmount
    };
  };

  const returns = calculateReturns();

  const handlePackageSelect = (pkg) => {
    setSelectedPackage(pkg);
    setAmount(pkg.minAmount.toString());
  };

  const handleAmountChange = (e) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    setAmount(value);
  };

  const handleContinue = () => {
    if (selectedPackage && amount &&
        parseInt(amount) >= selectedPackage.minAmount &&
        parseInt(amount) <= selectedPackage.maxAmount) {
      setStep(2);
    }
  };

  const handleBack = () => {
    if (step === 2) {
      // 2. aşamadayken 1. aşamaya geri dön
      setStep(1);
    } else {
      // 1. aşamadayken ana sayfaya çık
      onClose();
    }
  };

  const handleNetworkSelect = (network) => {
    setSelectedNetwork(network);

    // Mobilde QR kod alanına scroll
    setTimeout(() => {
      const qrSection = document.querySelector('.qr-section');
      if (qrSection && window.innerWidth <= 768) {
        qrSection.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }, 300);
  };

  const copyToClipboard = async () => {
    if (selectedNetwork) {
      await navigator.clipboard.writeText(selectedNetwork.address);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const isAmountValid = () => {
    if (!selectedPackage || !amount) return false;
    const numAmount = parseInt(amount);
    return numAmount >= selectedPackage.minAmount && numAmount <= selectedPackage.maxAmount;
  };

  return (
    <div className="investment-page-overlay" onClick={onClose}>
      <div className="investment-page" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="investment-header">
          <div className="header-left">
            <button className="back-btn" onClick={handleBack}>
              <ArrowLeft size={20} />
            </button>
            <h1>
              <TrendingUp size={24} />
              {step === 1 ? t('investment.title.new') : t('investment.title.payment')}
            </h1>
          </div>
          <div className="step-indicator">
            <div className={`step ${step >= 1 ? 'active' : ''}`}>1</div>
            <div className={`step-line ${step >= 2 ? 'active' : ''}`}></div>
            <div className={`step ${step >= 2 ? 'active' : ''}`}>2</div>
          </div>
        </div>

        {/* Content */}
        <div className="investment-content">
          {step === 1 ? (
            <>
              {/* Sol taraf - Paket Seçimi */}
              <div className="packages-section">
                <h2>{t('investment.packages.title')}</h2>
                <div className="packages-grid">
                  {packages.map((pkg) => (
                    <div 
                      key={pkg.id}
                      className={`package-card ${selectedPackage?.id === pkg.id ? 'selected' : ''} ${pkg.color}`}
                      onClick={() => handlePackageSelect(pkg)}
                    >
                      <div className="package-header">
                        <h3>{pkg.name}</h3>
                        <div className="package-apr">{pkg.apr}% APR</div>
                      </div>
                      
                      <div className="package-info">
                        <div className="info-item">
                          <DollarSign size={16} />
                          <span>${pkg.minAmount.toLocaleString()} - ${pkg.maxAmount.toLocaleString()}</span>
                        </div>
                        <div className="info-item">
                          <Calendar size={16} />
                          <span>{pkg.duration} {t('investment.duration')}</span>
                        </div>
                      </div>

                      <ul className="package-features">
                        {pkg.features.map((feature, index) => (
                          <li key={index}>
                            <CheckCircle size={14} />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>

              {/* Sağ taraf - Tutar ve Hesaplama */}
              <div className="calculation-section">
                {selectedPackage ? (
                  <>
                    <h2>{t('investment.calculation.title')}</h2>
                    
                    <div className="amount-input-section">
                      <label>{t('investment.amount.label')}</label>
                      <div className="amount-input-wrapper">
                        <span className="currency">$</span>
                        <input
                          type="text"
                          value={amount}
                          onChange={handleAmountChange}
                          placeholder={`${selectedPackage.minAmount} - ${selectedPackage.maxAmount}`}
                          className="amount-input"
                        />
                        <span className="currency-label">USD</span>
                      </div>
                      
                      {amount && !isAmountValid() && (
                        <div className="error-message">
                          <AlertCircle size={16} />
                          Tutar {selectedPackage.minAmount} - {selectedPackage.maxAmount} arasında olmalıdır
                        </div>
                      )}
                    </div>

                    {returns && isAmountValid() && (
                      <div className="returns-preview">
                        <div className="returns-grid">
                          <div className="return-item">
                            <span className="label">{t('investment.returns.monthly')}</span>
                            <span className="value">${returns.monthlyReturn.toFixed(2)}</span>
                          </div>
                          <div className="return-item">
                            <span className="label">{t('investment.returns.total')}</span>
                            <span className="value">${returns.totalReturn.toFixed(2)}</span>
                          </div>
                          <div className="return-item highlight">
                            <span className="label">{t('investment.returns.final')}</span>
                            <span className="value">${returns.finalAmount.toFixed(2)}</span>
                          </div>
                        </div>
                      </div>
                    )}

                    <button 
                      className="continue-btn"
                      onClick={handleContinue}
                      disabled={!isAmountValid()}
                    >
                      <CreditCard size={20} />
                      {t('investment.button.continue')}
                    </button>
                  </>
                ) : (
                  <div className="no-selection">
                    <TrendingUp size={48} />
                    <h3>{t('investment.no_selection.title')}</h3>
                    <p>{t('investment.no_selection.description')}</p>
                  </div>
                )}
              </div>
            </>
          ) : (
            <>
              {/* Sol taraf - İşlem Özeti */}
              <div className="summary-section">
                <h2>{t('investment.summary.title')}</h2>

                <div className="summary-and-network">
                  {/* İşlem Özeti Kartı */}
                  <div className="summary-card">
                    <div className="summary-header">
                      <h3>{selectedPackage.name}</h3>
                      <span className="apr-badge">{selectedPackage.apr}% APR</span>
                    </div>

                    <div className="summary-details">
                      <div className="detail-row">
                        <span>{t('investment.amount.label')}:</span>
                        <span>${parseInt(amount).toLocaleString()}</span>
                      </div>
                      <div className="detail-row">
                        <span>{t('investment.payment.duration')}:</span>
                        <span>{selectedPackage.duration} {t('investment.duration')}</span>
                      </div>
                      <div className="detail-row">
                        <span>{t('investment.returns.monthly')}:</span>
                        <span>${returns.monthlyReturn.toFixed(2)}</span>
                      </div>
                      <div className="detail-row">
                        <span>{t('investment.returns.total')}:</span>
                        <span>${returns.totalReturn.toFixed(2)}</span>
                      </div>
                      <div className="detail-row highlight">
                        <span>{t('investment.returns.final')}:</span>
                        <span>${returns.finalAmount.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>

                  {/* Ağ Seçimi - Sağ tarafa */}
                  <div className="network-selection-right">
                    <h3>{t('investment.network.title')}</h3>
                    <div className="networks-grid-right">
                      {networks.map((network) => (
                        <div
                          key={network.id}
                          className={`network-card-right ${selectedNetwork?.id === network.id ? 'selected' : ''}`}
                          onClick={() => handleNetworkSelect(network)}
                        >
                          <div className="network-header-right">
                            <Network size={16} />
                            <span className="network-name-right">{network.name}</span>
                            <span className="network-fee-right">{network.fee}</span>
                          </div>
                        </div>
                      ))}
                    </div>

                    <button
                      className="edit-btn-compact"
                      onClick={() => setStep(1)}
                    >
                      <Edit3 size={16} />
                      {t('investment.button.edit')}
                    </button>
                  </div>
                </div>
              </div>

              {/* Sağ taraf - Sadece Ödeme Detayları */}
              <div className="payment-section">
                {selectedNetwork ? (
                  <>
                    <h2>{t('investment.wallet.title')} ({selectedNetwork.name})</h2>

                    <div className="payment-details">
                      <div className="wallet-section">
                        <div className="wallet-input">
                          <input
                            type="text"
                            value={selectedNetwork.address}
                            readOnly
                            className="wallet-address"
                          />
                          <button
                            className={`copy-btn ${copied ? 'copied' : ''}`}
                            onClick={copyToClipboard}
                          >
                            <Copy size={16} />
                            {copied ? t('investment.copied') : t('investment.button.copy')}
                          </button>
                        </div>
                      </div>

                      <div className="qr-section">
                        <div className="qr-placeholder">
                          <QrCode size={64} />
                          <p>{t('investment.payment.qr')}</p>
                          <span>{selectedNetwork.name} {t('investment.payment.address')}</span>
                        </div>
                      </div>

                      <div className="payment-info">
                        <div className="info-item">
                          <span>{t('investment.payment.amount')}:</span>
                          <span>${parseInt(amount).toLocaleString()} {selectedNetwork.symbol}</span>
                        </div>
                        <div className="info-item">
                          <span>{t('investment.payment.network')}:</span>
                          <span>{selectedNetwork.fee}</span>
                        </div>
                        <div className="info-item">
                          <span>{t('investment.payment.duration')}:</span>
                          <span>{selectedNetwork.time}</span>
                        </div>
                      </div>

                      <button className="confirm-payment-btn">
                        <Wallet size={20} />
                        {t('investment.button.confirm')}
                      </button>
                    </div>
                  </>
                ) : (
                  <div className="no-network-selected">
                    <Network size={48} />
                    <h3>Ağ Seçin</h3>
                    <p>Ödeme yapmak için sol taraftan bir ağ seçin</p>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default InvestmentPage;
