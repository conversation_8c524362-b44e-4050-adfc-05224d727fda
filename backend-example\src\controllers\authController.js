import jwt from 'jsonwebtoken';
import { validationResult } from 'express-validator';
import User from '../models/User.js';
import { sendVerificationEmail, sendPasswordResetEmail } from '../utils/email.js';

// JWT token oluştur
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE || '24h'
  });
};

// Kullanıcı kaydı
export const register = async (req, res) => {
  try {
    // Validation kontrolü
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Geçersiz veriler',
        errors: errors.array()
      });
    }

    const { email, password, firstName, lastName, phone } = req.body;

    // Email adresini küçük harfe çevir (mail adresleri büyük/küçük harf duyarsız)
    const normalizedEmail = email.toLowerCase().trim();

    // Email kontrolü
    const existingUser = await User.findByEmail(normalizedEmail);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'Bu email adresi zaten kullanılıyor'
      });
    }

    // Yeni kullanıcı oluştur
    const user = new User({
      email: normalizedEmail,
      password,
      firstName,
      lastName,
      phone
    });

    // Doğrulama token'ı oluştur
    const verificationToken = user.createVerificationToken();
    
    // Kullanıcıyı kaydet
    await user.save();

    // Doğrulama emaili gönder
    try {
      await sendVerificationEmail(normalizedEmail, verificationToken, firstName);
    } catch (emailError) {
      console.error('Email gönderme hatası:', emailError);
      // Email hatası olsa bile kayıt devam etsin
    }

    res.status(201).json({
      success: true,
      message: 'Hesabınız oluşturuldu. Lütfen email adresinizi doğrulayın.',
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          isVerified: user.isVerified
        }
      }
    });

  } catch (error) {
    console.error('Kayıt hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.'
    });
  }
};

// Kullanıcı girişi
export const login = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Geçersiz veriler',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // Email adresini küçük harfe çevir (mail adresleri büyük/küçük harf duyarsız)
    const normalizedEmail = email.toLowerCase().trim();

    // Kullanıcıyı bul (şifre ile birlikte)
    const user = await User.findByEmail(normalizedEmail).select('+password');
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Geçersiz email veya şifre'
      });
    }

    // Hesap kilitli mi kontrol et
    if (user.isLocked) {
      return res.status(423).json({
        success: false,
        message: 'Hesabınız geçici olarak kilitlendi. Lütfen daha sonra tekrar deneyin.'
      });
    }

    // Şifre kontrolü
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      // Başarısız giriş denemesini kaydet
      user.loginAttempts += 1;
      
      // 5 başarısız denemeden sonra hesabı kilitle
      if (user.loginAttempts >= 5) {
        user.lockUntil = Date.now() + 30 * 60 * 1000; // 30 dakika
      }
      
      await user.save();
      
      return res.status(401).json({
        success: false,
        message: 'Geçersiz email veya şifre'
      });
    }

    // Hesap aktif mi kontrol et
    if (!user.isActive) {
      return res.status(403).json({
        success: false,
        message: 'Hesabınız deaktif edilmiş. Lütfen destek ekibi ile iletişime geçin.'
      });
    }

    // Başarılı giriş - sayaçları sıfırla
    user.loginAttempts = 0;
    user.lockUntil = undefined;
    user.lastLogin = new Date();
    await user.save();

    // JWT token oluştur
    const token = generateToken(user._id);

    res.status(200).json({
      success: true,
      message: 'Giriş başarılı',
      data: {
        token,
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          isVerified: user.isVerified,
          role: user.role,
          totalInvested: user.totalInvested,
          totalEarnings: user.totalEarnings
        }
      }
    });

  } catch (error) {
    console.error('Giriş hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.'
    });
  }
};

// Email doğrulama
export const verifyEmail = async (req, res) => {
  try {
    const { token } = req.params;

    const user = await User.findOne({
      verificationToken: token,
      verificationExpires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Geçersiz veya süresi dolmuş doğrulama token\'ı'
      });
    }

    // Email'i doğrula
    user.isVerified = true;
    user.verificationToken = undefined;
    user.verificationExpires = undefined;
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Email adresiniz başarıyla doğrulandı'
    });

  } catch (error) {
    console.error('Email doğrulama hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
};

// Şifre sıfırlama isteği
export const forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    const user = await User.findByEmail(email);
    if (!user) {
      // Güvenlik için her zaman başarılı mesaj döndür
      return res.status(200).json({
        success: true,
        message: 'Eğer bu email adresi sistemde kayıtlıysa, şifre sıfırlama linki gönderildi.'
      });
    }

    // Reset token oluştur
    const resetToken = user.createPasswordResetToken();
    await user.save();

    // Reset emaili gönder
    try {
      await sendPasswordResetEmail(email, resetToken, user.firstName);
    } catch (emailError) {
      console.error('Reset email gönderme hatası:', emailError);
      user.passwordResetToken = undefined;
      user.passwordResetExpires = undefined;
      await user.save();
      
      return res.status(500).json({
        success: false,
        message: 'Email gönderilirken hata oluştu'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Şifre sıfırlama linki email adresinize gönderildi.'
    });

  } catch (error) {
    console.error('Şifre sıfırlama hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
};

// Şifre sıfırlama
export const resetPassword = async (req, res) => {
  try {
    const { token } = req.params;
    const { password } = req.body;

    const user = await User.findOne({
      passwordResetToken: token,
      passwordResetExpires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Geçersiz veya süresi dolmuş reset token\'ı'
      });
    }

    // Yeni şifreyi ayarla
    user.password = password;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    user.loginAttempts = 0;
    user.lockUntil = undefined;
    
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Şifreniz başarıyla güncellendi'
    });

  } catch (error) {
    console.error('Şifre sıfırlama hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
};

// Token doğrulama
export const verifyToken = async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Kullanıcı bulunamadı'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          isVerified: user.isVerified,
          role: user.role
        }
      }
    });

  } catch (error) {
    console.error('Token doğrulama hatası:', error);
    res.status(500).json({
      success: false,
      message: 'Sunucu hatası'
    });
  }
};
