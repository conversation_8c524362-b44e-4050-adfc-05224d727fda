.dashboard {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  position: relative;
  overflow: hidden;
}

/* Matrix Rain Animation */
.matrix-rain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  pointer-events: none;
  z-index: 0; /* <PERSON><PERSON><PERSON><PERSON>n arkasında kalması için düşürüldü */
  overflow: hidden;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
}

.matrix-column {
  position: absolute;
  top: 0;
  width: 12px;
  height: 100%;
  animation: matrixRain linear infinite;
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  contain: layout style paint;
  overflow: hidden;
  pointer-events: none;
}

.matrix-char {
  display: block;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.1;
  color: var(--matrix-color);
  text-shadow: 0 0 3px var(--matrix-glow);
  contain: layout style;
  animation: charFade 3s ease-in-out infinite;
}

/* Character fade animation for trailing effect */
@keyframes charFade {
  0% { opacity: 1; }
  50% { opacity: 0.8; }
  100% { opacity: 0.4; }
}

/* Light Theme Matrix Colors */
:root {
  --matrix-color: rgba(0, 0, 0, 0.35);
  --matrix-glow: rgba(0, 0, 0, 0.2);
}

/* Dark Theme Matrix Colors */
[data-theme="dark"] {
  --matrix-color: rgba(255, 255, 255, 0.3);
  --matrix-glow: rgba(255, 255, 255, 0.18);
}

/* Aktif Akan Matrix */
@keyframes matrixRain {
  from {
    transform: translate3d(0, -50vh, 0);
  }
  to {
    transform: translate3d(0, 150vh, 0);
  }
}

/* Mobil Matrix Animasyonu - Daha uzun ve seyrek */
@media (max-width: 768px) {
  @keyframes matrixRainMobile {
    from {
      transform: translate3d(0, -100vh, 0);
    }
    to {
      transform: translate3d(0, 300vh, 0);
    }
  }

  .matrix-column {
    animation: matrixRainMobile linear infinite;
    width: 16px; /* Biraz daha geniş */
  }

  .matrix-char {
    font-size: 16px; /* Biraz daha büyük */
    line-height: 1.3;
    opacity: 0.6; /* Daha şeffaf */
  }
}

.dashboard-header {
  background: linear-gradient(135deg, rgba(var(--bg-secondary-rgb), 0.95) 0%, rgba(var(--bg-tertiary-rgb), 0.95) 100%);
  border-bottom: 1px solid var(--border-color);
  padding: 1.5rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}





.user-avatar {
  width: 48px;
  height: 48px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border: 2px solid var(--border-color);
  box-shadow: var(--shadow-md);
  flex-shrink: 0;
  overflow: hidden;
}

.user-avatar svg {
  width: 24px;
  height: 24px;
}

.user-details h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.user-email {
  margin: 0;
  color: var(--text-muted);
  font-size: 0.875rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Döviz Kurları - Hızlı istatistiklerle tamamen aynı */
.exchange-rates {
  background: rgba(var(--bg-secondary-rgb), 0.85);
  border: 1px solid rgba(var(--border-color-rgb), 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  box-shadow: var(--shadow-md), 0 0 20px rgba(99, 102, 241, 0.05);
  margin-top: 1.5rem;
  position: relative;
  z-index: 2;
}

.exchange-rates h4 {
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.currency-symbol {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  color: var(--accent-primary);
  flex-shrink: 0;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Ana sayfadaki dil seçici tasarımı - Dashboard için */
.language-selector {
  position: relative;
  display: flex;
  align-items: center;
}

.language-dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 2px solid var(--border-color);
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 4rem;
}

.language-dropdown-trigger:hover {
  border-color: var(--accent-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.language-dropdown-trigger .flag-image {
  width: 1.5rem;
  height: 1.125rem;
  object-fit: cover;
  border-radius: 0.25rem;
}

.dropdown-arrow {
  color: var(--text-secondary);
  transition: transform 0.3s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.language-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  background: var(--bg-secondary);
  border: 2px solid var(--border-color);
  border-radius: 0.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 10rem;
  overflow: hidden;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

/* Light mode - siyah yazı */
[data-theme="light"] .language-option {
  color: #000000;
}

[data-theme="light"] .language-option:hover {
  background: var(--bg-tertiary);
  color: #000000;
}

/* Dark mode - beyaz yazı */
[data-theme="dark"] .language-option {
  color: #ffffff;
}

[data-theme="dark"] .language-option:hover {
  background: var(--bg-tertiary);
  color: #ffffff;
}

.language-option:hover {
  background: var(--bg-tertiary);
}

.language-option.active {
  background: var(--accent-primary);
  color: white;
}

/* Dark mode için dil seçici kontrast iyileştirmesi */
[data-theme="dark"] .language-dropdown {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-xl);
}

/* Active state için ortak stil */
.language-option.active {
  background: var(--accent-primary) !important;
  color: white !important;
}

.theme-toggle,
.notification-btn,
.settings-btn,
.logout-btn {
  width: 40px;
  height: 40px;
  border: 2px solid var(--border-color);
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  position: relative;
}

/* Theme toggle - mobil float renklerini kullan */
[data-theme="light"] .theme-toggle {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-color: #3b82f6;
  color: white;
}

[data-theme="light"] .theme-toggle:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  border-color: #1d4ed8;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 0 15px rgba(59, 130, 246, 0.3);
}

[data-theme="dark"] .theme-toggle {
  background: linear-gradient(135deg, #f97316, #ea580c);
  border-color: #f97316;
  color: white;
}

[data-theme="dark"] .theme-toggle:hover {
  background: linear-gradient(135deg, #ea580c, #dc2626);
  border-color: #ea580c;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 0 15px rgba(249, 115, 22, 0.3);
}

.notification-btn:hover,
.settings-btn:hover {
  border-color: var(--accent-primary);
  background: var(--accent-primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.logout-btn:hover {
  border-color: #ef4444;
  background: #ef4444;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--accent-orange);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.dashboard-content {
  padding: 2rem 0 2rem 0; /* Alt padding footer için */
  position: relative;
  z-index: 10;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.investments-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.investment-card {
  background: rgba(var(--bg-secondary-rgb), 0.85);
  border: 1px solid rgba(var(--border-color-rgb), 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: var(--shadow-md), 0 0 20px rgba(99, 102, 241, 0.08);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.investment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: 1rem 1rem 0 0;
}

.investment-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl), 0 0 30px rgba(99, 102, 241, 0.15);
  border-color: rgba(var(--accent-primary-rgb), 0.4);
}

.investment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.investment-package {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.investment-package svg {
  color: var(--accent-primary);
}

.investment-package h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.investment-status {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background: var(--accent-green);
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 1rem;
  text-transform: uppercase;
  margin-top: 0.25rem;
}

.investment-amount-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.balance-toggle-inline {
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
  opacity: 0.7;
}

.balance-toggle-inline:hover {
  background: var(--accent-primary);
  color: white;
  transform: scale(1.05);
  opacity: 1;
  border-color: var(--accent-primary);
}

.investment-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-primary);
}

.package-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.package-type {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-weight: 500;
}

.investment-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 500;
}

.stat-value {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 1rem;
  font-weight: 600;
}

.stat-value.positive {
  color: var(--accent-green);
}

.stat-value.negative {
  color: #ef4444;
}

.stat-value.highlight {
  color: var(--accent-primary);
  font-weight: 700;
}

.investment-progress {
  margin-bottom: 1rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-success);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.investment-footer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-muted);
  font-size: 0.875rem;
}

.start-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.no-investment-state {
  text-align: center;
  padding: 3rem 2rem;
  background: var(--bg-secondary);
  border: 2px dashed var(--border-color);
  border-radius: 1rem;
}

.no-investment-icon {
  margin: 0 auto 1.5rem;
  width: 80px;
  height: 80px;
  background: var(--bg-tertiary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
}

.no-investment-state h3 {
  margin: 0 0 1rem;
  font-size: 1.5rem;
  color: var(--text-primary);
}

.no-investment-state p {
  margin: 0 0 2rem;
  color: var(--text-muted);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.quick-stats {
  background: rgba(var(--bg-secondary-rgb), 0.85);
  border: 1px solid rgba(var(--border-color-rgb), 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  box-shadow: var(--shadow-md), 0 0 20px rgba(99, 102, 241, 0.05);
  position: relative;
  z-index: 2;
}

.quick-stats h4 {
  margin: 0 0 1rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  margin-bottom: 0.75rem;
}

.stat-card:last-child {
  margin-bottom: 0;
}

.stat-card svg {
  color: var(--accent-primary);
}

.stat-number {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-card .stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem 0;
  }

  .user-info {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }

  .user-avatar {
    width: 48px;
    height: 48px;
    border: 2px solid var(--border-color);
    flex-shrink: 0;
  }

  .user-avatar svg {
    width: 24px;
    height: 24px;
  }

  .user-details {
    flex: 1;
    min-width: 0;
  }

  .user-details h2 {
    font-size: 1.125rem;
    margin-bottom: 0.125rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .user-email {
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .header-content {
    display: grid;
    grid-template-columns: auto 1fr auto auto;
    align-items: center;
    gap: 1rem;
  }

  /* Language selector solda */
  .language-selector {
    order: 0;
    grid-column: 1;
  }

  /* User info ortada */
  .user-info {
    order: 1;
    grid-column: 2;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.75rem;
    text-align: left;
    justify-self: center;
  }

  /* Nav toggle sağda */
  .mobile-nav-toggle {
    order: 2;
    grid-column: 3;
  }

  .header-actions {
    order: 3;
    grid-column: 4;
    width: auto;
    justify-content: flex-end;
    gap: 0.75rem;
  }

  /* Mobilde desktop öğeleri gizle, mobile öğeleri göster */
  .desktop-only {
    display: none !important;
  }

  .mobile-only {
    display: flex !important;
  }

  .notification-btn,
  .settings-btn,
  .logout-btn {
    width: 44px;
    height: 44px;
    border-radius: 0.75rem;
  }

  /* Theme toggle mobilde floating olarak gösterilecek */
  .theme-toggle.desktop-only {
    display: none !important;
  }

  .language-selector {
    order: 1;
  }

  .language-dropdown-trigger {
    min-width: 44px;
    min-height: 44px;
    padding: 0.5rem;
  }



  .investment-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .investment-amount-container {
    flex-direction: row-reverse;
    justify-content: flex-end;
    gap: 0.5rem;
  }

  .investment-amount {
    font-size: 1.25rem;
  }

  .balance-toggle-inline {
    width: 28px;
    height: 28px;
  }
}

/* Dashboard Footer */
.dashboard-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0;
  border-top: 1px solid var(--border-color);
  background: var(--bg-primary);
  backdrop-filter: blur(10px);
  z-index: 100;
  height: auto;
}

.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  margin: 0;
  padding: 0.5rem 0;
}

.footer-text {
  margin: 0;
  color: var(--text-muted);
  font-size: 0.875rem;
}

.footer-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.footer-badge {
  padding: 0.2rem 0.6rem;
  border-radius: 1rem;
  font-size: 0.7rem;
  font-weight: 600;
  color: white;
}

.footer-badge.ssl {
  background: var(--accent-green);
}

.footer-badge.usdt {
  background: var(--accent-gold);
}

.footer-badge.support-24-7 {
  background: var(--accent-primary);
}

/* Floating Menu System */
.floating-menu-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

/* Main Support Button */
.floating-support-btn {
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Light mode - mavi/beyaz/gri */
[data-theme="light"] .floating-support-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

[data-theme="light"] .floating-support-btn:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), 0 0 20px rgba(59, 130, 246, 0.4);
}

[data-theme="light"] .floating-support-btn.active {
  background: linear-gradient(135deg, #1e40af, #1e3a8a);
  transform: rotate(45deg);
}

/* Dark mode - turuncu */
[data-theme="dark"] .floating-support-btn {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
}

[data-theme="dark"] .floating-support-btn:hover {
  background: linear-gradient(135deg, #ea580c, #dc2626);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), 0 0 20px rgba(249, 115, 22, 0.4);
}

[data-theme="dark"] .floating-support-btn.active {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: rotate(45deg);
}

.floating-support-btn:active {
  transform: translateY(0) scale(0.95);
}

/* Floating Menu Items */
.floating-menu-items {
  position: absolute;
  bottom: 0;
  right: 0;
}

.floating-menu-item {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeInUp 0.3s ease;
  position: absolute;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Floating Menu Items - Saat Dizilimi (Ana butondan 80px uzaklık) */

/* Telegram Button - Saat 10 yönü */
.telegram-btn {
  bottom: 69px; /* 80 * sin(60°) ≈ 69px */
  right: 40px;  /* 80 * cos(60°) = 40px */
}

[data-theme="light"] .telegram-btn {
  background: linear-gradient(135deg, #0088cc, #006699);
  color: white;
}

[data-theme="light"] .telegram-btn:hover {
  background: linear-gradient(135deg, #006699, #004466);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(0, 136, 204, 0.4);
}

[data-theme="dark"] .telegram-btn {
  background: linear-gradient(135deg, #0088cc, #006699);
  color: white;
}

[data-theme="dark"] .telegram-btn:hover {
  background: linear-gradient(135deg, #006699, #004466);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(0, 136, 204, 0.4);
}

/* WhatsApp Button - Saat 12 yönü */
.whatsapp-btn {
  bottom: 80px; /* Tam üst */
  right: 5px;   /* Ortalanmış */
}

[data-theme="light"] .whatsapp-btn {
  background: linear-gradient(135deg, #25d366, #128c7e);
  color: white;
}

[data-theme="light"] .whatsapp-btn:hover {
  background: linear-gradient(135deg, #128c7e, #075e54);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(37, 211, 102, 0.4);
}

[data-theme="dark"] .whatsapp-btn {
  background: linear-gradient(135deg, #25d366, #128c7e);
  color: white;
}

[data-theme="dark"] .whatsapp-btn:hover {
  background: linear-gradient(135deg, #128c7e, #075e54);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(37, 211, 102, 0.4);
}

/* Theme Button - Saat 2 yönü */
.theme-btn {
  bottom: 69px; /* 80 * sin(60°) ≈ 69px */
  right: -30px; /* 80 * cos(60°) = 40px (negatif çünkü sağa doğru) */
}

[data-theme="light"] .theme-btn {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
}

[data-theme="light"] .theme-btn:hover {
  background: linear-gradient(135deg, #4f46e5, #4338ca);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(99, 102, 241, 0.4);
}

[data-theme="dark"] .theme-btn {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

[data-theme="dark"] .theme-btn:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg), 0 0 15px rgba(245, 158, 11, 0.4);
}





/* Desktop/Mobile visibility classes */
.desktop-only {
  display: block;
}

.mobile-only {
  display: none;
}

/* Mobile Navigation Toggle */
.mobile-nav-toggle {
  width: 44px;
  height: 44px;
  border: 2px solid var(--border-color);
  background: var(--bg-secondary);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

.mobile-nav-toggle:hover {
  border-color: var(--accent-primary);
  background: var(--accent-primary);
  color: white;
  transform: translateY(-1px);
}

/* Mobile Navigation Dropdown */
.mobile-nav-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(15px);
  z-index: 1000;
  min-width: 200px;
  padding: 0.5rem;
  margin-top: 0.5rem;
}

.mobile-nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1rem;
  border: none;
  background: transparent;
  color: var(--text-primary);
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
  min-height: 48px;
  position: relative;
}

.mobile-nav-item:hover {
  background: var(--bg-tertiary);
  transform: translateX(4px);
}

.mobile-nav-item.logout-item:hover {
  background: #dc2626; /* Koyu kırmızı buton */
  color: white; /* Beyaz yazı */
  transform: translateX(4px);
}

.mobile-nav-item.logout-item {
  transition: all 0.3s ease;
}

.mobile-nav-item.logout-item:active {
  background: #b91c1c;
  transform: translateX(2px) scale(0.98);
}

.mobile-nav-badge {
  background: var(--accent-orange);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  margin-left: auto;
}



/* Responsive Footer */
@media (max-width: 768px) {
  .dashboard-footer {
    padding: 0;
  }

  .footer-content {
    gap: 1rem;
    flex-direction: column;
    padding: 0.4rem 0;
  }

  .footer-text {
    font-size: 0.8rem;
  }

  .footer-badges {
    gap: 0.4rem;
  }

  .footer-badge {
    font-size: 0.65rem;
    padding: 0.15rem 0.5rem;
  }

  .floating-menu-container {
    bottom: 1.5rem;
    right: 1.5rem;
  }

  .floating-support-btn {
    width: 52px;
    height: 52px;
  }

  .floating-menu-item {
    width: 44px;
    height: 44px;
  }

  /* Telegram - Saat 10 yönü (70px uzaklık) */
  .telegram-btn {
    bottom: 61px; /* 70 * sin(60°) ≈ 61px */
    right: 35px;  /* 70 * cos(60°) = 35px */
  }

  /* WhatsApp - Saat 12 yönü */
  .whatsapp-btn {
    bottom: 70px; /* Tam üst */
    right: 2px;   /* Ortalanmış */
  }

  /* Theme - Saat 2 yönü */
  .theme-btn {
    bottom: 61px; /* 70 * sin(60°) ≈ 61px */
    right: -27px; /* 70 * cos(60°) = 35px (negatif) */
  }

  .floating-theme-btn {
    width: 52px;
    height: 52px;
    bottom: 7.5rem; /* Support butonunun üzerine */
    right: 1.5rem;
  }



  .dashboard-content {
    padding: 2rem 0 2.5rem 0;
  }

  /* Döviz kurları mobilde alt padding */
  .exchange-rates {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0.75rem 0;
    gap: 0.75rem;
  }

  .header-content {
    grid-template-columns: auto 1fr auto;
    gap: 0.75rem;
  }

  .user-avatar {
    width: 44px;
    height: 44px;
    border: 2px solid var(--border-color);
  }

  .user-avatar svg {
    width: 22px;
    height: 22px;
  }

  .user-details h2 {
    font-size: 1rem;
  }

  .user-email {
    font-size: 0.75rem;
  }

  .header-actions {
    gap: 0.5rem;
  }



  .theme-toggle,
  .notification-btn,
  .settings-btn,
  .logout-btn {
    width: 40px;
    height: 40px;
    border-radius: 0.5rem;
  }

  .language-dropdown-trigger {
    min-width: 40px;
    min-height: 40px;
    padding: 0.375rem;
  }



  .language-dropdown {
    min-width: 8rem;
    right: 0;
  }

  .language-option {
    padding: 0.75rem;
    min-height: 44px;
  }

  .dashboard-footer {
    padding: 0;
  }

  .footer-content {
    gap: 0.75rem;
    padding: 0.3rem 0;
  }

  .footer-text {
    font-size: 0.75rem;
  }

  .footer-badges {
    gap: 0.3rem;
  }

  .floating-menu-container {
    bottom: 1rem;
    right: 1rem;
  }

  .floating-support-btn {
    width: 48px;
    height: 48px;
  }

  .floating-menu-item {
    width: 40px;
    height: 40px;
  }

  /* Telegram - Saat 10 yönü (60px uzaklık) */
  .telegram-btn {
    bottom: 52px; /* 60 * sin(60°) ≈ 52px */
    right: 30px;  /* 60 * cos(60°) = 30px */
  }

  /* WhatsApp - Saat 12 yönü */
  .whatsapp-btn {
    bottom: 60px; /* Tam üst */
    right: 4px;   /* Ortalanmış */
  }

  /* Theme - Saat 2 yönü */
  .theme-btn {
    bottom: 52px; /* 60 * sin(60°) ≈ 52px */
    right: -22px; /* 60 * cos(60°) = 30px (negatif) */
  }

  .footer-badge {
    font-size: 0.6rem;
    padding: 0.12rem 0.45rem;
  }

  .floating-support-btn {
    width: 48px;
    height: 48px;
    bottom: 3rem;
    right: 1rem;
  }

  .floating-theme-btn {
    width: 48px;
    height: 48px;
    bottom: 7rem; /* Support butonunun üzerine */
    right: 1rem;
  }



  .dashboard-content {
    padding: 1.5rem 0 2rem 0;
  }
}
