@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

:root {
  /* Light theme colors - Modern Finance Palette */
  --bg-primary: #ffffff;
  --bg-primary-rgb: 255, 255, 255;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #64748b;

  /* Modern Gradient Colors */
  --accent-primary: #6366f1;
  --accent-secondary: #4f46e5;
  --accent-tertiary: #7c3aed;
  --accent-gold: #f59e0b;
  --accent-green: #16a34a;
  --accent-cyan: #06b6d4;
  --accent-pink: #ec4899;
  --accent-orange: #f97316;

  /* Gradient Combinations */
  --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
  --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 50%, #6366f1 100%);
  --gradient-gold: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
  --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-earnings: linear-gradient(135deg, #f97316 0%, #10b981 100%);
  --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);

  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-glow: 0 0 20px rgb(99 102 241 / 0.3);
}

[data-theme="dark"] {
  /* Dark theme colors - YouTube Dark Style */
  --bg-primary: #0f0f0f;
  --bg-primary-rgb: 15, 15, 15;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #272727;
  --text-primary: #ffffff;
  --text-secondary: #aaaaaa;
  --text-muted: #717171;

  /* Dark Mode Accent Colors */
  --accent-primary: #818cf8;
  --accent-secondary: #6366f1;
  --accent-tertiary: #a855f7;
  --accent-gold: #fbbf24;
  --accent-green: #22c55e;
  --accent-cyan: #22d3ee;
  --accent-pink: #f472b6;
  --accent-orange: #fb923c;

  /* Dark Mode Gradients */
  --gradient-primary: linear-gradient(135deg, #818cf8 0%, #a855f7 50%, #f472b6 100%);
  --gradient-secondary: linear-gradient(135deg, #22d3ee 0%, #60a5fa 50%, #818cf8 100%);
  --gradient-gold: linear-gradient(135deg, #fbbf24 0%, #fb923c 100%);
  --gradient-success: linear-gradient(135deg, #34d399 0%, #10b981 100%);
  --gradient-earnings: linear-gradient(135deg, #fb923c 0%, #34d399 100%);
  --gradient-dark: linear-gradient(135deg, #374151 0%, #1f2937 100%);

  --border-color: #3f3f3f;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.4);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
  --shadow-glow: 0 0 20px rgb(129 140 248 / 0.5);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /* Tarayıcı müdahalelerini engelle */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* Tarayıcı renk müdahalelerini engelle */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

html {
  scroll-behavior: smooth;
  zoom: 0.95; /* Tüm sayfayı %95 oranında küçült */
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
  /* Ek koruma önlemleri */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  /* Tarayıcı renk zorlamasını engelle */
  color-scheme: light dark;
  forced-color-adjust: none;
  overflow-x: hidden;
}

/* Input alanları için özel izinler */
input, textarea, select, button {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
  font-weight: 800;
}

h2 {
  font-size: 2rem;
  font-weight: 700;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
}

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

/* Global section title underline */
.section-title {
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: 2px;
}

/* Calculator title underline */
.calculator-title {
  position: relative;
  display: inline-block;
}

.calculator-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: 2px;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
  border: 1px solid transparent;
}

.btn-primary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--accent-primary);
  color: white;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Section spacing */
.section {
  padding: 4rem 0;
}

.section-lg {
  padding: 6rem 0;
}

/* Cards */
.card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.card-highlight {
  border: 2px solid var(--accent-primary);
  background: linear-gradient(135deg, var(--accent-primary)10, var(--bg-secondary));
}

/* Grid */
.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Responsive */
@media (max-width: 768px) {
  html {
    font-size: 14px; /* Base font size küçültme */
  }

  h1 {
    font-size: 1.875rem;
    line-height: 1.2;
  }

  h2 {
    font-size: 1.5rem;
    line-height: 1.3;
  }

  h3 {
    font-size: 1.25rem;
    line-height: 1.4;
  }

  /* Mobile underline adjustments */
  .section-title::after,
  .calculator-title::after {
    width: 60px;
    height: 2.5px;
    bottom: -8px;
  }

  .section {
    padding: 2.5rem 0;
  }

  .section-lg {
    padding: 3.5rem 0;
  }

  .card {
    padding: 1.25rem;
  }

  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .btn-lg {
    padding: 0.875rem 1.25rem;
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 13px; /* Daha da küçük font */
  }

  h1 {
    font-size: 1.625rem;
    line-height: 1.2;
  }

  h2 {
    font-size: 1.375rem;
    line-height: 1.3;
  }

  h3 {
    font-size: 1.125rem;
    line-height: 1.4;
  }

  /* Small mobile underline adjustments */
  .section-title::after,
  .calculator-title::after {
    width: 50px;
    height: 2px;
    bottom: -6px;
  }

  .section {
    padding: 2rem 0;
  }

  .section-lg {
    padding: 3rem 0;
  }

  .card {
    padding: 1rem;
  }

  .btn {
    padding: 0.625rem 0.875rem;
    font-size: 0.85rem;
  }

  .btn-lg {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .container {
    padding: 0 1rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes textShimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--accent-primary);
  }
  50% {
    box-shadow: 0 0 20px var(--accent-primary), 0 0 30px var(--accent-primary);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.scale-in {
  animation: scaleIn 0.6s ease-out;
}

.float {
  animation: float 3s ease-in-out infinite;
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* Koruma önlemleri */
.no-select {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* Tarayıcı zorlamasını engelle */
html {
  color-scheme: light dark;
  forced-color-adjust: none;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

/* Utilities */
.text-center {
  text-align: center;
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-gold {
  background: var(--gradient-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-success {
  background: var(--gradient-success);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-earnings {
  background: var(--gradient-earnings);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-earnings-glow {
  color: var(--accent-green);
  animation: earningsPulse 3s ease-in-out infinite;
  font-weight: 700;
}

.usdt-text {
  color: #26A17B !important;
  font-weight: 700 !important;
}

@keyframes earningsPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.text-shimmer {
  background: linear-gradient(
    90deg,
    var(--text-primary) 0%,
    var(--accent-primary) 50%,
    var(--text-primary) 100%
  );
  background-size: 200% auto;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textShimmer 3s linear infinite;
}

.text-glow {
  color: var(--accent-primary);
  text-shadow: 0 0 10px var(--accent-primary);
}

.text-typing {
  overflow: hidden;
  border-right: 2px solid var(--accent-primary);
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--accent-primary); }
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-8 {
  margin-top: 2rem;
}
